"""
AudioPanel Component for pAIrSEEption

This module contains the AudioPanel class which handles:
- Whisper speech recognition settings and controls
- TTS (Text-to-Speech) settings and controls
- Push-to-Talk functionality
- Audio recording controls and settings
- Voice interaction interface

Author: pAIrSEEption Team
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                               QPushButton, QLabel, QComboBox, QCheckBox, QSlider)
from PySide6.QtCore import Qt, Slot, Signal


class AudioPanel(QWidget):
    """Panel for audio controls including Whisper and TTS settings"""
    
    # Signals for audio control events
    whisper_model_changed = Signal(str)  # model_name
    language_changed = Signal(str)  # language_code
    beam_size_changed = Signal(int)  # beam_size
    save_audio_changed = Signal(bool)  # enabled
    tts_enabled_changed = Signal(bool)  # enabled
    push_to_talk_started = Signal()
    push_to_talk_stopped = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_push_to_talk_active = False
        self.space_key_pressed = False
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the AudioPanel user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Whisper Controls Group
        self.setup_whisper_section(layout)
        
        # Voice Interaction Group
        self.setup_voice_interaction_section(layout)
        
        # TTS Controls Group
        self.setup_tts_section(layout)
        
        layout.addStretch()  # Push content to top
        
    def setup_whisper_section(self, parent_layout):
        """Setup the Whisper configuration section"""
        whisper_group = QGroupBox("Voice Transcription (Whisper)")
        whisper_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        whisper_layout = QVBoxLayout(whisper_group)
        
        # Whisper Model Selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("🧠 Model:"))
        
        self.whisper_model_combo = QComboBox()
        self.whisper_model_combo.addItems(["tiny", "base", "small", "medium", "large"])
        self.whisper_model_combo.setCurrentText("base")
        self.whisper_model_combo.currentTextChanged.connect(self.on_whisper_model_changed)
        self.whisper_model_combo.setMinimumHeight(30)
        self.whisper_model_combo.setToolTip("Note: 'large' uses large-v3-turbo for better efficiency, 'medium' models may use significant GPU memory.\nWill try GPU first, then fallback to CPU if needed.")
        model_layout.addWidget(self.whisper_model_combo)
        model_layout.addStretch()
        whisper_layout.addLayout(model_layout)
        
        # Language Selection
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("🌍 Language:"))
        
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "auto (Auto-detect)",
            "de German",
            "en English",
            "fr French",
            "es Spanish",
            "it Italian"
        ])
        self.language_combo.setCurrentText("de German")
        self.language_combo.currentTextChanged.connect(self.on_language_changed)
        self.language_combo.setMinimumHeight(30)
        lang_layout.addWidget(self.language_combo)
        lang_layout.addStretch()
        whisper_layout.addLayout(lang_layout)
        
        # Beam Size Selection
        beam_layout = QHBoxLayout()
        beam_layout.addWidget(QLabel("🔍 Beam Size:"))
        
        self.beam_size_combo = QComboBox()
        self.beam_size_combo.addItems(["1", "2", "3", "4", "5"])
        self.beam_size_combo.setCurrentText("1")
        self.beam_size_combo.currentTextChanged.connect(self.on_beam_size_changed)
        self.beam_size_combo.setMinimumHeight(30)
        self.beam_size_combo.setToolTip("Higher beam size may improve accuracy but increases processing time")
        beam_layout.addWidget(self.beam_size_combo)
        beam_layout.addStretch()
        whisper_layout.addLayout(beam_layout)
        
        # Save Audio Files Checkbox
        self.save_audio_checkbox = QCheckBox("💾 Save Audio Files")
        self.save_audio_checkbox.setChecked(False)
        self.save_audio_checkbox.stateChanged.connect(self.on_save_audio_changed)
        self.save_audio_checkbox.setToolTip("Save recorded audio files for debugging")
        self.save_audio_checkbox.setStyleSheet(self._get_checkbox_style())
        whisper_layout.addWidget(self.save_audio_checkbox)
        
        parent_layout.addWidget(whisper_group)
        
    def setup_voice_interaction_section(self, parent_layout):
        """Setup the voice interaction section"""
        voice_group = QGroupBox("Voice Interaction")
        voice_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        voice_layout = QVBoxLayout(voice_group)
        
        # Status Label
        self.voice_status_label = QLabel("Mode: Push-to-Talk (Button or Spacebar)")
        self.voice_status_label.setStyleSheet("""
            QLabel {
                background-color: #333;
                color: #00ff00;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 4px;
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
        """)
        voice_layout.addWidget(self.voice_status_label)
        
        # Push-to-Talk Button
        self.push_to_talk_button = QPushButton("🎤 Hold to Talk (Space)")
        self.push_to_talk_button.setMinimumHeight(40)
        self.push_to_talk_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
                transform: translateY(1px);
            }
        """)
        self.push_to_talk_button.pressed.connect(self.start_push_to_talk_recording)
        self.push_to_talk_button.released.connect(self.stop_push_to_talk_recording)
        voice_layout.addWidget(self.push_to_talk_button)
        
        parent_layout.addWidget(voice_group)
        
    def setup_tts_section(self, parent_layout):
        """Setup the TTS configuration section"""
        tts_group = QGroupBox("Text-to-Speech (TTS)")
        tts_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        tts_layout = QVBoxLayout(tts_group)
        
        # TTS Enable Checkbox
        self.cb_enable_tts = QCheckBox("🔊 Enable Text-to-Speech")
        self.cb_enable_tts.setChecked(False)
        self.cb_enable_tts.stateChanged.connect(self.on_tts_enabled_changed)
        self.cb_enable_tts.setToolTip("Enable voice output for LLM responses using Thorsten VITS")
        self.cb_enable_tts.setStyleSheet(self._get_checkbox_style())
        tts_layout.addWidget(self.cb_enable_tts)
        
        parent_layout.addWidget(tts_group)
        
    def _get_checkbox_style(self):
        """Get consistent checkbox styling"""
        return """
            QCheckBox {
                color: white;
                font-size: 13px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #2196F3;
                border: 2px solid #2196F3;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #1976D2;
                border: 2px solid #1976D2;
            }
        """
        
    # === Signal Handlers ===
    
    @Slot(str)
    def on_whisper_model_changed(self, model_name):
        """Handle Whisper model selection change"""
        self.whisper_model_changed.emit(model_name)
        
    @Slot(str)
    def on_language_changed(self, language_text):
        """Handle language selection change"""
        # Extract language code from text like "de German"
        language_code = language_text.split(" ")[0] if language_text != "auto (Auto-detect)" else None
        self.language_changed.emit(language_code or "auto")
        
    @Slot(str)
    def on_beam_size_changed(self, beam_size_str):
        """Handle beam size selection change"""
        try:
            beam_size = int(beam_size_str)
            self.beam_size_changed.emit(beam_size)
        except ValueError:
            pass  # Invalid beam size, ignore
            
    @Slot(int)
    def on_save_audio_changed(self, state):
        """Handle save audio checkbox change"""
        enabled = state == Qt.CheckState.Checked.value
        self.save_audio_changed.emit(enabled)
        
    @Slot(int)
    def on_tts_enabled_changed(self, state):
        """Handle TTS enable checkbox change"""
        enabled = state == Qt.CheckState.Checked.value
        self.tts_enabled_changed.emit(enabled)
        
    # === Push-to-Talk Methods ===
    
    def start_push_to_talk_recording(self):
        """Start Push-to-Talk recording"""
        if not self.is_push_to_talk_active:
            self.is_push_to_talk_active = True
            self.voice_status_label.setText("🔴 Recording... (Release button/spacebar to stop)")
            self.push_to_talk_button.setText("🔴 Recording...")
            self.push_to_talk_started.emit()
            
    def stop_push_to_talk_recording(self):
        """Stop Push-to-Talk recording"""
        if self.is_push_to_talk_active:
            self.is_push_to_talk_active = False
            self.voice_status_label.setText("Mode: Push-to-Talk (Button or Spacebar)")
            self.push_to_talk_button.setText("🎤 Hold to Talk (Space)")
            self.push_to_talk_stopped.emit()
            
    # === Keyboard Event Handling ===
    
    def handle_key_press(self, key):
        """Handle keyboard press events (called from MainWindow)"""
        if key == Qt.Key_Space:
            if not self.space_key_pressed:
                self.space_key_pressed = True
                self.start_push_to_talk_recording()
                
    def handle_key_release(self, key):
        """Handle keyboard release events (called from MainWindow)"""
        if key == Qt.Key_Space:
            if self.space_key_pressed:
                self.space_key_pressed = False
                self.stop_push_to_talk_recording()
                
    # === Getter Methods ===
    
    def get_current_whisper_model(self):
        """Get currently selected Whisper model"""
        return self.whisper_model_combo.currentText()
        
    def get_current_language(self):
        """Get currently selected language"""
        language_text = self.language_combo.currentText()
        return language_text.split(" ")[0] if language_text != "auto (Auto-detect)" else None
        
    def get_current_beam_size(self):
        """Get currently selected beam size"""
        try:
            return int(self.beam_size_combo.currentText())
        except ValueError:
            return 1
            
    def is_save_audio_enabled(self):
        """Check if save audio is enabled"""
        return self.save_audio_checkbox.isChecked()
        
    def is_tts_enabled(self):
        """Check if TTS is enabled"""
        return self.cb_enable_tts.isChecked()
        
    def is_recording_active(self):
        """Check if push-to-talk recording is active"""
        return self.is_push_to_talk_active
