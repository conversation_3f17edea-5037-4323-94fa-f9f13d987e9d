"""
AI Panel Component for pAIrSEEption

This module contains the AI Vision Analysis panel with LLM provider selection,
model configuration, and scene analysis functionality.
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QComboBox,
    QStackedWidget, QLineEdit, QPushButton, QTextBrowser
)
from PySide6.QtCore import Signal, Qt

# Import configuration constants
try:
    from core import (
        AVAILABLE_ONLINE_MODELS, DEFAULT_ONLINE_MODEL,
        AVAILABLE_OLLAMA_MODELS, DEFAULT_OLLAMA_MODEL
    )
except ImportError:
    # Fallback values if core module not available
    AVAILABLE_ONLINE_MODELS = {"Gemini 3 27B": "google/gemini-3-27b"}
    DEFAULT_ONLINE_MODEL = "Gemini 3 27B"
    AVAILABLE_OLLAMA_MODELS = {"MiniCPM-V 8B": "minicpm-v:8b"}
    DEFAULT_OLLAMA_MODEL = "MiniCPM-V 8B"


class AIPanel(QWidget):
    """
    AI Vision Analysis Panel Component
    
    Provides LLM provider selection (OpenRouter/Ollama), model configuration,
    and scene analysis functionality with response display.
    """
    
    # Signals for communication with MainWindow
    provider_changed = Signal(str)  # Provider selection changed
    online_model_changed = Signal(str)  # OpenRouter model changed
    ollama_model_changed = Signal(str)  # Ollama model changed
    api_key_set = Signal(str)  # OpenRouter API key set
    server_updated = Signal(str, str)  # Ollama server IP and port updated
    analyze_scene_requested = Signal()  # Scene analysis button clicked
    anchor_clicked = Signal(str)  # Link clicked in response display
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the AI panel user interface"""
        # Main layout
        layout = QVBoxLayout(self)
        
        # AI Vision Analysis Group
        ai_group = QGroupBox("AI Vision Analysis")
        ai_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        ai_layout = QVBoxLayout(ai_group)
        
        # Provider selection dropdown
        provider_header = QHBoxLayout()
        provider_header.addWidget(QLabel("🌐 AI Provider:"))
        provider_header.addStretch()
        ai_layout.addLayout(provider_header)
        
        self.provider_combo = QComboBox()
        self.provider_combo.addItems(["OpenRouter", "Ollama"])
        self.provider_combo.setCurrentText("OpenRouter")
        self.provider_combo.setMinimumHeight(30)
        ai_layout.addWidget(self.provider_combo)
        
        # Stack widget to hold provider-specific settings
        self.provider_settings_stack = QStackedWidget()
        ai_layout.addWidget(self.provider_settings_stack)
        
        # Setup provider-specific pages
        self.setup_openrouter_page()
        self.setup_ollama_page()
        
        # Send button with improved styling
        self.send_scene_button = QPushButton("📷 Analyze Current Scene")
        self.send_scene_button.setMinimumHeight(40)
        self.send_scene_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        ai_layout.addWidget(self.send_scene_button)
        
        # AI Response display with header
        response_header = QHBoxLayout()
        response_header.addWidget(QLabel("🔍 AI Scenegraph:"))
        response_header.addStretch()
        ai_layout.addLayout(response_header)
        
        self.ai_response_display = QTextBrowser()
        self.ai_response_display.setMinimumHeight(400)
        self.ai_response_display.setPlaceholderText(
            "AI analysis results will appear here...\n\n"
            "Click 'Analyze Current Scene' or say 'test' to get started!"
        )
        self.ai_response_display.setStyleSheet("""
            QTextBrowser {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: #2b2b2b;
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
            }
        """)
        # Enable HTML support for formatted output
        self.ai_response_display.setHtml("")
        self.ai_response_display.setAcceptRichText(True)
        self.ai_response_display.setReadOnly(True)
        # Enable anchor/link handling
        self.ai_response_display.setTextInteractionFlags(
            Qt.TextSelectableByMouse | Qt.TextSelectableByKeyboard | Qt.LinksAccessibleByMouse
        )
        self.ai_response_display.setOpenLinks(False)
        ai_layout.addWidget(self.ai_response_display)
        
        layout.addWidget(ai_group)
        layout.addStretch()
    
    def setup_openrouter_page(self):
        """Setup OpenRouter configuration page"""
        openrouter_page = QWidget()
        openrouter_layout = QVBoxLayout(openrouter_page)
        
        # Model selection with improved layout
        model_header = QHBoxLayout()
        model_header.addWidget(QLabel("🧠 OpenRouter Models:"))
        model_header.addStretch()
        openrouter_layout.addLayout(model_header)
        
        self.online_model_combo = QComboBox()
        self.online_model_combo.addItems(list(AVAILABLE_ONLINE_MODELS.keys()))
        self.online_model_combo.setCurrentText(DEFAULT_ONLINE_MODEL)
        self.online_model_combo.setMinimumHeight(30)
        openrouter_layout.addWidget(self.online_model_combo)
        
        # OpenRouter API Key input
        api_key_header = QHBoxLayout()
        api_key_header.addWidget(QLabel("🔑 OpenRouter API Key:"))
        api_key_header.addStretch()
        openrouter_layout.addLayout(api_key_header)
        
        api_key_input_layout = QHBoxLayout()
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("Enter your OpenRouter API key...")
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setMinimumHeight(30)
        self.api_key_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                background-color: #2b2b2b;
                color: white;
                font-size: 11px;
            }
        """)
        api_key_input_layout.addWidget(self.api_key_input, 3)  # 3/4 of the width
        
        self.set_api_key_button = QPushButton("✓ Set")
        self.set_api_key_button.setMinimumWidth(70)
        self.set_api_key_button.setMinimumHeight(30)
        self.set_api_key_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        api_key_input_layout.addWidget(self.set_api_key_button, 1)  # 1/4 of the width
        openrouter_layout.addLayout(api_key_input_layout)
        
        self.provider_settings_stack.addWidget(openrouter_page)
    
    def setup_ollama_page(self):
        """Setup Ollama configuration page"""
        ollama_page = QWidget()
        ollama_layout = QVBoxLayout(ollama_page)
        
        # Ollama model selection
        ollama_model_header = QHBoxLayout()
        ollama_model_header.addWidget(QLabel("🧠 Ollama Models:"))
        ollama_model_header.addStretch()
        ollama_layout.addLayout(ollama_model_header)
        
        self.ollama_model_combo = QComboBox()
        self.ollama_model_combo.addItems(list(AVAILABLE_OLLAMA_MODELS.keys()))
        self.ollama_model_combo.setCurrentText(DEFAULT_OLLAMA_MODEL)
        self.ollama_model_combo.setMinimumHeight(30)
        ollama_layout.addWidget(self.ollama_model_combo)
        
        # Ollama server configuration
        server_header = QHBoxLayout()
        server_header.addWidget(QLabel("🖥️ Ollama Server:"))
        server_header.addStretch()
        ollama_layout.addLayout(server_header)
        
        # Server URL layout
        server_url_layout = QHBoxLayout()
        self.server_ip_input = QLineEdit()
        self.server_ip_input.setPlaceholderText("Server IP (default: *************)")
        self.server_ip_input.setText("*************")
        self.server_ip_input.setMinimumHeight(30)
        self.server_ip_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                background-color: #2b2b2b;
                color: white;
                font-size: 11px;
            }
        """)
        server_url_layout.addWidget(self.server_ip_input, 3)
        
        self.server_port_input = QLineEdit()
        self.server_port_input.setPlaceholderText("Port (default: 11434)")
        self.server_port_input.setText("11434")
        self.server_port_input.setMaximumWidth(100)
        self.server_port_input.setMinimumHeight(30)
        self.server_port_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                background-color: #2b2b2b;
                color: white;
                font-size: 11px;
            }
        """)
        server_url_layout.addWidget(self.server_port_input, 1)
        
        self.update_server_button = QPushButton("✓ Update")
        self.update_server_button.setMinimumWidth(70)
        self.update_server_button.setMinimumHeight(30)
        self.update_server_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        server_url_layout.addWidget(self.update_server_button, 1)
        ollama_layout.addLayout(server_url_layout)
        
        self.provider_settings_stack.addWidget(ollama_page)
    
    def connect_signals(self):
        """Connect internal signals to slots"""
        self.provider_combo.currentTextChanged.connect(self.on_provider_changed)
        self.online_model_combo.currentTextChanged.connect(self.online_model_changed.emit)
        self.ollama_model_combo.currentTextChanged.connect(self.ollama_model_changed.emit)
        self.set_api_key_button.clicked.connect(self.on_set_api_key)
        self.update_server_button.clicked.connect(self.on_update_server)
        self.send_scene_button.clicked.connect(self.analyze_scene_requested.emit)
        self.ai_response_display.anchorClicked.connect(self.anchor_clicked.emit)
    
    def on_provider_changed(self, provider):
        """Handle provider selection change"""
        # Update stack widget to show appropriate settings
        if provider == "OpenRouter":
            self.provider_settings_stack.setCurrentIndex(0)
        else:  # Ollama
            self.provider_settings_stack.setCurrentIndex(1)
        
        self.provider_changed.emit(provider)
    
    def on_set_api_key(self):
        """Handle API key set button click"""
        api_key = self.api_key_input.text().strip()
        if api_key:
            self.api_key_set.emit(api_key)
    
    def on_update_server(self):
        """Handle server update button click"""
        ip = self.server_ip_input.text().strip()
        port = self.server_port_input.text().strip()
        if ip and port:
            self.server_updated.emit(ip, port)
    
    def get_current_provider(self):
        """Get currently selected provider"""
        return self.provider_combo.currentText()
    
    def get_current_online_model(self):
        """Get currently selected OpenRouter model"""
        return self.online_model_combo.currentText()
    
    def get_current_ollama_model(self):
        """Get currently selected Ollama model"""
        return self.ollama_model_combo.currentText()
    
    def display_ai_response(self, response, model_name, model_color="#3498db"):
        """Display AI response with formatting"""
        import time
        current_time = time.strftime("%H:%M:%S", time.localtime())
        
        # Format the output with HTML for color support
        formatted_output = (
            f'<span style="color:#666;">[{current_time}]</span> '
            f'<span style="color:{model_color};font-weight:bold;">{model_name}</span>: '
            f'{response}'
        )
        
        # Append to existing content
        current_html = self.ai_response_display.toHtml()
        if current_html.strip():
            new_html = current_html + "<br>" + formatted_output
        else:
            new_html = formatted_output
        
        self.ai_response_display.setHtml(new_html)
        
        # Scroll to bottom to show latest response
        scrollbar = self.ai_response_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
