"""
RobotPanel Component for pAIrSEEption

This module contains the RobotPanel class which handles:
- Robot control and navigation interface
- Object selector widget integration
- Navigation commands and movement controls
- Target selection and distance settings

Author: pAIrSEEption Team
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                               QPushButton, QLabel)
from PySide6.QtCore import Qt, Slot, Signal

# Import robot control if available
try:
    from object_selector_gui import ObjectSelectorWidget
    ROBOT_CONTROL_AVAILABLE = True
except ImportError:
    ROBOT_CONTROL_AVAILABLE = False
    ObjectSelectorWidget = None


class RobotPanel(QWidget):
    """Panel for robot control and navigation functionality"""
    
    # Signals for robot control events
    robot_control_ready = Signal(object)  # Emitted when robot control is ready
    navigation_started = Signal(str, int)  # object_type, object_id
    navigation_stopped = Signal()
    robot_error = Signal(str)  # Error message
    
    def __init__(self, parent=None, tts_worker=None):
        super().__init__(parent)
        self.parent_window = parent
        self.tts_worker = tts_worker
        self.object_selector_widget = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the RobotPanel user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Robot Control Group
        robot_group = QGroupBox("Robot Control")
        robot_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        robot_layout = QVBoxLayout(robot_group)
        
        if ROBOT_CONTROL_AVAILABLE:
            # Status label
            self.status_label = QLabel("Robot control available - initializing...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #333;
                    color: #00ff00;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 10px;
                }
            """)
            robot_layout.addWidget(self.status_label)
            
            # Placeholder for object selector widget (will be added later)
            self.robot_placeholder = QLabel("Robot control will be initialized after TTS setup...")
            self.robot_placeholder.setStyleSheet("""
                QLabel {
                    background-color: #2b2b2b;
                    color: white;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 10px;
                    font-size: 12px;
                }
            """)
            self.robot_placeholder.setAlignment(Qt.AlignCenter)
            robot_layout.addWidget(self.robot_placeholder)
            
        else:
            # Robot control not available
            self.status_label = QLabel("Robot control not available")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #333;
                    color: #ff6666;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 10px;
                }
            """)
            robot_layout.addWidget(self.status_label)
            
            info_label = QLabel("Robot control is disabled in configuration or object_selector_gui.py is not available.")
            info_label.setStyleSheet("""
                QLabel {
                    background-color: #2b2b2b;
                    color: #cccccc;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 10px;
                    font-size: 11px;
                }
            """)
            info_label.setWordWrap(True)
            robot_layout.addWidget(info_label)
        
        layout.addWidget(robot_group)
        layout.addStretch()  # Push content to top
        
    def initialize_robot_control(self, tts_worker=None):
        """Initialize robot control widget after TTS worker is available"""
        if not ROBOT_CONTROL_AVAILABLE:
            return False
            
        if tts_worker:
            self.tts_worker = tts_worker
            
        try:
            # Create object selector widget for robot navigation with TTS support
            self.object_selector_widget = ObjectSelectorWidget(self.parent_window, tts_worker=self.tts_worker)
            
            # Replace placeholder with actual widget
            if hasattr(self, 'robot_placeholder') and self.robot_placeholder:
                # Find the robot group layout
                robot_group = self.findChild(QGroupBox, "Robot Control")
                if robot_group:
                    robot_layout = robot_group.layout()
                    # Remove placeholder
                    robot_layout.removeWidget(self.robot_placeholder)
                    self.robot_placeholder.deleteLater()
                    self.robot_placeholder = None
                    
                    # Add object selector widget
                    robot_layout.addWidget(self.object_selector_widget)
            
            # Set camera dimensions for image-based orientation
            if hasattr(self.object_selector_widget, 'husky_controller') and self.object_selector_widget.husky_controller:
                # ZED camera default resolution (will be updated when camera starts)
                self.object_selector_widget.husky_controller.set_camera_dimensions(1280, 720)
            
            # Update status
            self.status_label.setText("Robot control ready")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #333;
                    color: #00ff00;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 10px;
                }
            """)
            
            # Emit signal that robot control is ready
            self.robot_control_ready.emit(self.object_selector_widget)
            
            return True
            
        except Exception as e:
            error_msg = f"Failed to initialize robot control: {e}"
            self.status_label.setText("Robot control failed")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #333;
                    color: #ff6666;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 10px;
                }
            """)
            self.robot_error.emit(error_msg)
            return False
    
    def update_objects(self, object_data_list):
        """Update the object selector with currently detected objects"""
        if self.object_selector_widget and object_data_list:
            self.object_selector_widget.update_objects(object_data_list)
    
    def get_object_selector(self):
        """Get the object selector widget for voice intent system"""
        return self.object_selector_widget
    
    def set_camera_dimensions(self, width, height):
        """Set camera dimensions for robot control"""
        if (self.object_selector_widget and 
            hasattr(self.object_selector_widget, 'husky_controller') and 
            self.object_selector_widget.husky_controller):
            self.object_selector_widget.husky_controller.set_camera_dimensions(width, height)
    
    def shutdown_robot_control(self):
        """Shutdown robot control safely"""
        if self.object_selector_widget:
            try:
                self.object_selector_widget.shutdown()
                return True
            except Exception as e:
                self.robot_error.emit(f"Failed to shutdown robot control: {e}")
                return False
        return True
    
    def is_robot_control_available(self):
        """Check if robot control is available and initialized"""
        return ROBOT_CONTROL_AVAILABLE and self.object_selector_widget is not None
    
    # === Navigation Control Methods ===
    
    def navigate_to_object(self, object_type, object_id, follow_mode=False):
        """Navigate to a specific object"""
        if not self.object_selector_widget:
            self.robot_error.emit("Robot control not available")
            return False
            
        try:
            self.object_selector_widget.class_input.setText(object_type)
            self.object_selector_widget.id_input.setValue(object_id)
            self.object_selector_widget.follow_mode.setChecked(follow_mode)
            self.object_selector_widget._on_navigate_clicked()
            
            self.navigation_started.emit(object_type, object_id)
            return True
            
        except Exception as e:
            self.robot_error.emit(f"Navigation failed: {e}")
            return False
    
    def stop_navigation(self):
        """Stop current navigation"""
        if not self.object_selector_widget:
            self.robot_error.emit("Robot control not available")
            return False
            
        try:
            self.object_selector_widget._on_stop_clicked()
            self.navigation_stopped.emit()
            return True
            
        except Exception as e:
            self.robot_error.emit(f"Stop navigation failed: {e}")
            return False
    
    def set_target_distance(self, distance):
        """Set target distance for navigation"""
        if not self.object_selector_widget:
            self.robot_error.emit("Robot control not available")
            return False
            
        try:
            self.object_selector_widget.target_distance.setValue(distance)
            return True
            
        except Exception as e:
            self.robot_error.emit(f"Set distance failed: {e}")
            return False
