"""
GUI Components Module

This module contains all GUI components for the pAIrSEEption application.
Each component is designed to be modular and reusable.

Components:
- VideoPanel: Video display and camera controls
- ControlPanel: YOLO model controls and display options  
- AIPanel: LLM integration and voice interface
- InfoPanel: Scene information and logging
- RobotPanel: Robot control and navigation
"""

# Import only available components
__all__ = []

try:
    from .video_panel import VideoPanel
    __all__.append('VideoPanel')
except ImportError:
    pass

try:
    from .control_panel import ControlPanel
    __all__.append('ControlPanel')
except ImportError:
    pass

try:
    from .ai_panel import AIPanel
    __all__.append('AIPanel')
except ImportError:
    pass

# TODO: Add other components as they are implemented
# from .info_panel import InfoPanel
# from .robot_panel import RobotPanel
