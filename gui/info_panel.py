"""
InfoPanel Component for pAIrSEEption

This module contains the InfoPanel class which handles:
- System log display and management
- Scene object information display
- Performance statistics display
- Transcription display for speech recognition

Author: pAIrSEEption Team
"""

import time
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                               QTextEdit, QLabel, QSplitter)
from PySide6.QtCore import Qt, Slot, Signal
from PySide6.QtGui import QFont, QTextCursor


class InfoPanel(QWidget):
    """Panel for displaying information, logs, statistics, and transcriptions"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the InfoPanel user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # Statistics Display (compact at top)
        self.setup_statistics_section(splitter)
        
        # Transcription Display (compact)
        self.setup_transcription_section(splitter)
        
        # Scene Information Display
        self.setup_scene_info_section(splitter)
        
        # System Log Display
        self.setup_log_section(splitter)
        
        # Set initial splitter proportions
        splitter.setSizes([50, 80, 300, 300])  # Stats, Transcription, Scene Info, Log
        
    def setup_statistics_section(self, parent):
        """Setup the statistics display section"""
        stats_group = QGroupBox("Performance Statistics")
        stats_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_display = QLabel("Objects: 0 | FPS: 0.0 | Active: 0\nCPU: 0.0% | GPU: 0.0% | RAM: 0.0% | VRAM: 0.0%")
        self.stats_display.setStyleSheet("""
            QLabel {
                background-color: #333;
                color: #00ff00;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 4px;
                font-family: 'Courier New', monospace;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        self.stats_display.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.stats_display.setMaximumHeight(50)
        stats_layout.addWidget(self.stats_display)
        
        parent.addWidget(stats_group)
        
    def setup_transcription_section(self, parent):
        """Setup the transcription display section"""
        transcription_group = QGroupBox("Speech Transcription")
        transcription_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        transcription_layout = QVBoxLayout(transcription_group)
        
        self.transcription_display = QTextEdit()
        self.transcription_display.setMaximumHeight(80)
        self.transcription_display.setPlaceholderText("Transcribed speech will appear here...")
        self.transcription_display.setReadOnly(True)
        self.transcription_display.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: white;
                border: 2px solid #555;
                border-radius: 8px;
                padding: 8px;
                font-size: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        transcription_layout.addWidget(self.transcription_display)
        
        parent.addWidget(transcription_group)
        
    def setup_scene_info_section(self, parent):
        """Setup the scene information display section"""
        info_group = QGroupBox("Scene Information")
        info_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        info_layout = QVBoxLayout(info_group)
        
        self.info_display = QTextEdit()
        self.info_display.setMinimumHeight(300)
        self.info_display.setPlaceholderText("Scene object information will appear here...")
        self.info_display.setReadOnly(True)
        self.info_display.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: #2b2b2b;
                color: white;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        info_layout.addWidget(self.info_display)
        
        parent.addWidget(info_group)
        
    def setup_log_section(self, parent):
        """Setup the system log display section"""
        log_group = QGroupBox("System Log")
        log_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        log_layout = QVBoxLayout(log_group)
        
        self.log_display = QTextEdit()
        self.log_display.setMinimumHeight(300)
        self.log_display.setPlaceholderText("Log messages will appear here...")
        self.log_display.setReadOnly(True)
        self.log_display.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: #2b2b2b;
                color: white;
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
        """)
        log_layout.addWidget(self.log_display)
        
        parent.addWidget(log_group)
        
    # === Statistics Methods ===
    
    @Slot(dict)
    def update_statistics_display(self, stats):
        """Update the statistics display with current metrics"""
        objects = stats.get('objects', 0)
        fps = stats.get('fps', 0.0)
        active = stats.get('active', 0)
        frame_count = stats.get('frame_count', 0)
        cpu_percent = stats.get('cpu_percent', 0.0)
        gpu_percent = stats.get('gpu_percent', 0.0)
        memory_percent = stats.get('memory_percent', 0.0)
        gpu_memory_percent = stats.get('gpu_memory_percent', 0.0)
        
        # Create multi-line statistics display
        line1 = f"Objects: {objects} | FPS: {fps:.1f} | Active: {active} | Frames: {frame_count}"
        line2 = f"CPU: {cpu_percent:.1f}% | GPU: {gpu_percent:.1f}% | RAM: {memory_percent:.1f}% | VRAM: {gpu_memory_percent:.1f}%"
        stats_text = f"{line1}\n{line2}"
        
        # Add a note if we're on Jetson and GPU stats are 0
        if gpu_percent == 0.0 and gpu_memory_percent == 0.0:
            stats_text += "\n(Note: GPU stats may be logged to CSV even if not displayed)"
        
        self.stats_display.setText(stats_text)
        
    # === Log Methods ===
    
    @Slot(str)
    def log_message(self, message):
        """Add a message to the log display with timestamp"""
        current_time = time.strftime("%H:%M:%S", time.localtime())
        log_entry = f"[{current_time}] {message}"
        
        self.log_display.append(log_entry)
        
        # Keep only recent log entries (limit to 200 lines)
        if self.log_display.document().lineCount() > 200:
            cursor = self.log_display.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.Start)
            lines_to_remove = self.log_display.document().lineCount() - 200
            for _ in range(lines_to_remove):
                cursor.movePosition(QTextCursor.MoveOperation.Down, QTextCursor.MoveMode.KeepAnchor)
            cursor.removeSelectedText()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.log_display.setTextCursor(cursor)
            
        self.log_display.ensureCursorVisible()
        
    # === Scene Information Methods ===
    
    @Slot()
    def refresh_scene_objects_display(self, scene_data=None, distances_info=None):
        """Refresh the scene objects display with current data"""
        display_text_parts = ["--- Current Scene Objects ---"]
        
        if scene_data:
            for obj_data in scene_data:
                obj_id = obj_data.get('object_id', 'N/A')
                class_name = obj_data.get('class_name', 'N/A')
                position = obj_data.get('position', [])
                speed = obj_data.get('speed', 0)
                confidence = obj_data.get('confidence', 0)
                
                pos_str = f"[{position[0]:.2f}, {position[1]:.2f}, {position[2]:.2f}]" if position and len(position) == 3 else "[N/A]"
                display_text_parts.append(f"ID:{obj_id} {class_name} Pos:{pos_str} Speed:{speed:.1f}m/s Conf:{confidence:.0f}%")
        else:
            display_text_parts.append("No objects currently tracked.")

        display_text_parts.append("\n--- Relative Distances ---")
        if distances_info:
            for dist_data in distances_info:
                id1 = dist_data.get('id1', 'N/A')
                class1 = dist_data.get('class1', 'N/A')
                id2 = dist_data.get('id2', 'N/A')
                class2 = dist_data.get('class2', 'N/A')
                distance = dist_data.get('distance', 0)
                display_text_parts.append(f"ID:{id1}({class1}) <-> ID:{id2}({class2}): {distance:.2f}m")
        else:
            display_text_parts.append("No distance calculations available.")

        final_text = "\n".join(display_text_parts)
        self.info_display.setText(final_text)
        self.info_display.moveCursor(QTextCursor.MoveOperation.Start)
        
    # === Transcription Methods ===
    
    @Slot(str)
    def update_transcription_display(self, transcription):
        """Update the transcription display with new text"""
        self.transcription_display.setText(transcription)
        self.transcription_display.moveCursor(QTextCursor.MoveOperation.End)
        
    def clear_transcription_display(self):
        """Clear the transcription display"""
        self.transcription_display.clear()
        
    # === Utility Methods ===
    
    def clear_all_displays(self):
        """Clear all display areas"""
        self.log_display.clear()
        self.info_display.clear()
        self.transcription_display.clear()
        self.stats_display.setText("Objects: 0 | FPS: 0.0 | Active: 0\nCPU: 0.0% | GPU: 0.0% | RAM: 0.0% | VRAM: 0.0%")
