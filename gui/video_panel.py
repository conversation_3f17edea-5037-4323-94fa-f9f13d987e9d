"""
Video Panel Component

This module contains the video display and camera control components.
Handles video frame display and perception start/stop controls.
"""

import cv2
import numpy as np
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QGroupBox, QGridLayout
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QImage, QPixmap


class VideoPanel(QWidget):
    """Video display and camera control panel"""
    
    # Signals
    perception_toggle_requested = Signal()  # Signal when start/stop button is clicked
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.perception_running = False
        self.current_annotated_frame = None
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the video panel UI"""
        layout = QVBoxLayout(self)
        
        # Video Display
        self.video_label = QLabel("Video will appear here")
        self.video_label.setMinimumSize(800, 450)
        self.video_label.setStyleSheet("""
            border: 1px solid black; 
            background-color: #2b2b2b; 
            color: white;
            font-size: 14px;
            font-weight: bold;
        """)
        self.video_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.video_label)
        
        # Camera Control Group
        control_group = QGroupBox("Camera Controls")
        control_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        control_layout = QGridLayout(control_group)
        
        # Start/Stop Button
        self.start_button = QPushButton("Start pAIrSEEption")
        self.start_button.clicked.connect(self.on_start_button_clicked)
        self.start_button.setMinimumHeight(50)
        self.start_button.setStyleSheet(self._get_start_button_style())
        control_layout.addWidget(self.start_button, 0, 0)
        
        layout.addWidget(control_group)
    
    def _get_start_button_style(self):
        """Get the start button stylesheet"""
        return """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
    
    def _get_stop_button_style(self):
        """Get the stop button stylesheet"""
        return """
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """
    
    @Slot()
    def on_start_button_clicked(self):
        """Handle start/stop button click"""
        self.perception_toggle_requested.emit()
    
    def set_perception_running(self, running):
        """Update the perception running state and button appearance"""
        self.perception_running = running
        if running:
            self.start_button.setText("Stop pAIrSEEption")
            self.start_button.setStyleSheet(self._get_stop_button_style())
        else:
            self.start_button.setText("Start pAIrSEEption")
            self.start_button.setStyleSheet(self._get_start_button_style())
    
    @Slot(np.ndarray)
    def update_frame(self, frame_bgr_annotated):
        """Update the video display with a new frame"""
        try:
            # Store the current annotated frame for potential saving
            self.current_annotated_frame = frame_bgr_annotated.copy() if frame_bgr_annotated is not None else None
            
            if frame_bgr_annotated is None or frame_bgr_annotated.size == 0:
                self.video_label.setText("Error: Received empty frame")
                return
                
            h, w, ch = frame_bgr_annotated.shape
            if h == 0 or w == 0:
                self.video_label.setText("Error: Frame has zero dimension")
                return
                
            bytes_per_line = ch * w
            qt_format = QImage.Format_RGB888
            img_data_for_qimage = frame_bgr_annotated.data
            
            if ch == 3:
                rgb_frame = cv2.cvtColor(frame_bgr_annotated, cv2.COLOR_BGR2RGB)
                img_data_for_qimage = rgb_frame.data
                qt_format = QImage.Format_RGB888
            elif ch == 4:
                rgba_frame = cv2.cvtColor(frame_bgr_annotated, cv2.COLOR_BGRA2RGBA)
                img_data_for_qimage = rgba_frame.data
                qt_format = QImage.Format_RGBA8888
            else:
                # Handle grayscale or other formats
                if frame_bgr_annotated.ndim == 2:
                    img_data_for_qimage = frame_bgr_annotated.data
                    qt_format = QImage.Format_Grayscale8
                    bytes_per_line = w
                elif frame_bgr_annotated.ndim == 3 and ch > 0:
                    gray_frame = frame_bgr_annotated[:,:,0].copy()
                    img_data_for_qimage = gray_frame.data
                    qt_format = QImage.Format_Grayscale8
                    bytes_per_line = w
                else:
                    self.video_label.setText(f"Error: Cannot handle frame shape {frame_bgr_annotated.shape}")
                    return
            
            qt_image = QImage(img_data_for_qimage, w, h, bytes_per_line, qt_format)
            if qt_image.isNull():
                self.video_label.setText("Error: QImage conversion failed.")
                return
                
            pixmap = QPixmap.fromImage(qt_image)
            self.video_label.setPixmap(pixmap.scaled(
                self.video_label.size(), 
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            ))
            
        except Exception as e:
            print(f"VideoPanel Error updating frame: {e}")
            self.video_label.setText("Error displaying video frame")
    
    def get_current_frame(self):
        """Get the current annotated frame"""
        return self.current_annotated_frame
