#!/usr/bin/env python3
"""
pAIrSEEption - AI-Powered Robot Navigation System

Main entry point for the pAIrSEEption application.
This system provides AI-powered object detection, scene analysis, and robot navigation
for blind users using ZED camera, YOLO detection, and LLM integration.

Author: pAIrSEEption Team
"""

import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point for the pAIrSEEption application."""
    
    print("🚀 Starting pAIrSEEption...")
    print("=" * 50)
    
    try:
        # Step 1: Load configuration
        print("📋 Loading configuration...")
        from core import load_config, initialize_constants
        
        config = load_config('config.yaml')
        initialize_constants()
        print("✅ Configuration loaded successfully")
        
        # Step 2: Import and start the main application
        print("🎯 Initializing application...")
        from scenegraph_online_v0 import main as run_app
        
        print("✅ Application initialized")
        print("🎬 Starting GUI...")
        print("=" * 50)
        
        # Step 3: Run the application
        return run_app()
        
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
        return 0
    except Exception as e:
        print(f"❌ Failed to start pAIrSEEption: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
