# pAIrSEEption - Runtime Generated Files & Directories

# === Runtime-Generated Directories ===
# Clean images sent to AI models
image_to_online_llm/
image_to_ollama_llm/

# Annotated images with object detection overlays  
obj_seg_imgs/
obj_seg_ollama_imgs/

# AI model responses with timestamps
response_log/
response_ollama_log/

# === Log Files ===
# CSV files with object tracking data
*.csv
object_log.csv
relative_distances.csv

# === Model Files ===
# Downloaded YOLO/YOLOE model weights
*.pt
*.weights
*.onnx
*.engine
mobileclip_blt.ts

# === Cache & Temporary Files ===
# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so

# PyTorch cache
.cache/
*.pth

# Ultralytics cache
runs/
*.yaml

# === IDE & Editor Files ===
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml

# Vim
*.swp
*.swo
*~

# === OS Generated Files ===
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Desktop.ini
$RECYCLE.BIN/

# Linux
.directory
Saleh_tts/
# === API Keys & Secrets ===
# Environment variables and secrets (if moved to external files)
.env
.env.local
*.key
secrets.json

# === ZED SDK Files ===
# ZED calibration files (if auto-generated)
*.conf
calibration/

# === Video/Audio Recording Files ===
audio_recordings/
video_recordings/
tts_output/
*.wav
*.mp3
*.flac
audio_temp/

# === Backup Files ===
*.bak
*.backup
*.old
*.orig

# === Documentation Build ===
docs/_build/
site/

# === Package Manager ===
# pip
pip-log.txt
pip-delete-this-directory.txt

# === Virtual Environment ===
venv/
env/
ENV/

# === .py ===
v6_ollama.py
coqui_ai_tts_test.py
coqui_ai_tts_test_de.py
orca_tts.py
record_voice.py
setup_tts.py
voice_clone.py
whisper_test.py

GEMINI.md
