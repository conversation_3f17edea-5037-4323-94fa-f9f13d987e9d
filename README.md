# pAIrSEEption - AI-Powered Vision Assistant

A real-time computer vision application that combines ZED stereo cameras, YOLO object detection, speech recognition, and both cloud and local AI models to create an intelligent vision assistance system for accessibility and spatial awareness.

## Features

### 🎯 Object Detection & Tracking
- **Real-time YOLO/YOLOE object detection** with multiple model support (<PERSON><PERSON><PERSON>11, <PERSON><PERSON>O<PERSON>8, <PERSON><PERSON><PERSON>)
- **Text-prompted object filtering** for YOLOe models to dynamically specify detection classes
- **3D spatial tracking** using ZED stereo camera depth perception
- **Speed analysis** with static object detection and motion filtering
- **Object segmentation** with mask visualization
- **Distance measurements** between detected objects

### 🎤 Voice Interaction
- **Whisper-powered speech recognition** for hands-free control
- **Voice commands** for triggering AI scene analysis
- **Real-time audio transcription** with command processing

### 🤖 AI Vision Analysis - Multi-Provider Support
- **Cloud-based models via OpenRouter API**:
  - Google Gemini 3 27B, Gemini 2.0 Flash Experimental
  - Meta Llama 3.2 Vision & Llama 4 Maverick
  - <PERSON><PERSON><PERSON> 2.5 VL 72B
  - Mistral AI models
  - And more via OpenRouter API
- **Local models via Ollama API**:
  - MiniCPM-V 8B
  - Granite 3.2 Vision 2B
  - Llava-Llama3 8B
  - Llama3 Vision 11B
  - And other multimodal models available through Ollama
- **Scene description** optimized for accessibility
- **Image logging** with correlated request IDs

### 📊 Data Logging & Analysis
- **CSV logging** of object tracking data with timestamps
- **Scene information display** with real-time object positions
- **Distance calculations** between multiple objects
- **Performance monitoring** with comprehensive logging

## Installation

### Prerequisites
1. **Python 3.8+** with pip
2. **CUDA-compatible GPU** (recommended for YOLO models)
3. **ZED Camera** (ZED, ZED Mini, ZED 2, ZED 2i, or ZED X)
4. **Microphone** for speech recognition
5. **(Optional) Ollama instance** for local LLM support

### Step 1: Install Python Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Install ZED SDK
The ZED SDK must be installed separately:
1. Download from [Stereolabs website](https://www.stereolabs.com/developers/release/)
2. Follow the installation guide for your operating system
3. Install the Python API: `pip install pyzed`

### Step 3: Configure API Access
1. **Cloud LLMs**: Get a free API key from [OpenRouter](https://openrouter.ai/)
   - Add your key in the application UI
2. **Local LLMs**: Install [Ollama](https://ollama.ai/)
   - Set up the server IP and port in the application UI
   - Pull required multimodal models (e.g., `ollama pull llava-llama3`)

### Step 4: Configure Ollama for Network Access (Linux)
By default, Ollama only listens on localhost. To make it accessible from other machines on your network:

1. **Edit the systemd service file**:
   ```bash
   sudo systemctl edit ollama.service
   ```

2. **Add the following configuration** to make Ollama listen on all network interfaces:
   ```
   [Service]
   Environment="OLLAMA_HOST=0.0.0.0"
   ```

3. **Apply the changes**:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl restart ollama.service
   ```

4. **Verify the configuration**:
   ```bash
   # Check if Ollama is listening on all interfaces (0.0.0.0)
   sudo netstat -tulpn | grep ollama
   ```

5. **Configure firewall** if necessary:
   ```bash
   # For UFW firewall
   sudo ufw allow 11434/tcp
   
   # For firewalld
   sudo firewall-cmd --permanent --add-port=11434/tcp
   sudo firewall-cmd --reload
   ```

6. **In the pAIrSEEption application**, set the Ollama server to the IP address of the machine running Ollama.

> **Security Note**: Exposing Ollama to your network increases security risks. Consider implementing additional security measures like network isolation or VPN if using in production environments.

## Usage

### Starting the Application
```bash
python scenegraph_online_v0.py
```

### Main Interface Components

#### 1. **Video Display**
- Real-time camera feed with object detection overlays
- Adjustable confidence threshold slider
- Multiple visualization options (bounding boxes, masks, tracking info)

#### 2. **Model Selection**
- Choose between different YOLO/YOLOE models
- Hot-swappable model loading during runtime
- Support for both detection and segmentation models
- Dynamic text prompts for YOLOe models to filter specific object classes

#### 3. **YOLOe Text Prompt**
- Input field for specifying object classes to detect (e.g., "person, chair, tv")
- Apply button to update detection classes in real-time
- Only available when using YOLOe models (not prompt-free variants)
- Dynamically filter objects based on text prompts without reloading the model

#### 4. **Voice Control**
- Click "🎤 Listen and Transcribe" to record voice commands
- Say "test" to trigger AI scene analysis
- Transcriptions appear in real-time

#### 5. **AI Vision Analysis**
- Switch between OpenRouter (cloud) and Ollama (local) providers
- Select from multiple AI models for each provider
- Scene analysis with "📷 Analyze Current Scene" button
- AI responses displayed with model name and color highlighting
- Consistent request IDs for correlating images with responses

#### 6. **Scene Information Panel**
- Live object tracking data
- Distance measurements between objects
- Refresh button for manual updates

### Voice Commands
- **"test"** - Captures current scene and sends to AI for analysis

### Keyboard Shortcuts
- **ESC** - Close application
- **Space** - Start/stop perception (when focused on start button)

## Configuration

### Model Configuration
Edit the `AVAILABLE_YOLO_MODELS` dictionary to add custom models:
```python
AVAILABLE_YOLO_MODELS = {
    "Custom Model": "path/to/your/model.pt",
    # ... existing models
}
```

### ZED Camera Settings
Adjust camera parameters in the configuration section:
```python
ZED_RESOLUTION = sl.RESOLUTION.HD720  # HD720, HD1080, HD2K
ZED_DEPTH_MODE = sl.DEPTH_MODE.PERFORMANCE  # PERFORMANCE, QUALITY, ULTRA
ZED_FPS = 30  # Frames per second
```

### AI Model Selection
- Cloud models: Edit `AVAILABLE_ONLINE_MODELS` dictionary
- Local models: Edit `AVAILABLE_OLLAMA_MODELS` dictionary

## Output Files

The application generates several types of output, organized with consistent request IDs:

### 📁 Directories Created
- **OpenRouter (Cloud)**:
  - `image_to_online_llm/` - Clean images sent to AI models
  - `obj_seg_imgs/` - Annotated images with detection overlays
  - `response_log/` - AI model responses
- **Ollama (Local)**:
  - `image_to_ollama_llm/` - Clean images sent to local LLMs
  - `obj_seg_ollama_imgs/` - Annotated images with detection overlays
  - `response_ollama_log/` - Local LLM responses

### 📄 CSV Log Files
- `object_log.csv` - Detailed object tracking data
- `relative_distances.csv` - Distance measurements between objects

## Hardware Requirements

### Minimum Requirements
- **CPU**: 4-core processor
- **RAM**: 8GB
- **GPU**: NVIDIA with 4GB VRAM (for YOLO models)
- **Storage**: 1GB free space (excluding models)

### Recommended Setup
- **CPU**: 8-core processor
- **RAM**: 16GB
- **GPU**: NVIDIA with 8GB+ VRAM
- **Storage**: 5GB+ free space
- **Network**: Fast internet connection for cloud LLMs
- **Local LLM Server**: Separate machine with GPU for Ollama (optional)

## Troubleshooting

### Common Issues

1. **ZED Camera Not Detected**
   - Ensure ZED SDK is properly installed
   - Check USB 3.0 connection
   - Verify camera permissions

2. **CUDA Out of Memory**
   - Reduce model size (use nano models)
   - Lower camera resolution
   - Close other GPU applications

3. **Audio Issues**
   - Check microphone permissions
   - Install `sounddevice` dependencies
   - Verify audio device selection

4. **API Connection Problems**
   - **OpenRouter**: Verify API key and internet connection
   - **Ollama**: Check server IP/port and ensure models are pulled

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup
1. Fork the repository
2. Install development dependencies
3. Make your changes
4. Test thoroughly with different hardware setups
5. Submit a pull request

## License

**TBD**

## Acknowledgments

- **Stereolabs** for the ZED SDK and stereo vision technology
- **Ultralytics** for YOLO model implementations
- **OpenAI** for Whisper speech recognition
- **OpenRouter** for providing access to multiple cloud AI models
- **Ollama** for local LLM deployment solution
- **PySide6** for the GUI framework

## Contact

For questions, suggestions, or support, please open an issue on the repository.

---

**Note**: This application is designed as an accessibility tool and should be used as a supplementary aid. Always exercise caution and use additional safety measures when navigating environments.
