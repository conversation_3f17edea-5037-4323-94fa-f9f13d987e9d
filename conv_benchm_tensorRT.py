#!/usr/bin/env python3
"""
Create a TensorRT engine with optimized settings for better compatibility
and benchmark both original PyTorch and TensorRT models
Usage for benchmark only with existing engine file:
python3 conv_benchm_tensorRT.py --benchmark-only
OR convert and benchmark:
python3 conv_benchm_tensorRT.py --convert
"""
from ultralytics import YOLO
import torch
import numpy as np
import time
import os
import argparse

# Parse command line arguments
parser = argparse.ArgumentParser(description='Benchmark PyTorch and TensorRT models')
parser.add_argument('--convert', action='store_true', help='Force conversion to TensorRT even if engine file exists')
parser.add_argument('--benchmark-only', action='store_true', help='Skip conversion and only run benchmark')
args = parser.parse_args()

# check if CUDA is available
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# Function to benchmark model performance
def benchmark_model(model, name, num_runs=50):
    print(f"\n🧪 Benchmarking {name}...")
    
    # Create a test image
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # Warmup
    for _ in range(5):
        model(test_image, device=0, verbose=False)
    
    # Benchmark
    start_time = time.time()
    for _ in range(num_runs):
        results = model(test_image, device=0, verbose=False)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / num_runs
    fps = 1.0 / avg_time
    
    print(f"✅ {name} benchmark completed:")
    print(f"   - Average inference time: {avg_time*1000:.2f} ms")
    print(f"   - Estimated FPS: {fps:.1f}")
    print(f"   - Results count: {len(results)}")
    if results and len(results) > 0:
        result = results[0]
        print(f"   - Boxes detected: {len(result.boxes) if result.boxes is not None else 0}")
        print(f"   - Masks available: {result.masks is not None}")
    
    return avg_time, fps

try:
    # Define file paths
    pt_model_path = "yolo11l.pt"
    trt_model_path = "yolo11l.engine"
    
    # Load the original PyTorch YOLO model
    print(f"\n📂 Loading original PyTorch model ({pt_model_path})...")
    pt_model = YOLO(pt_model_path)
    pt_model.to('cuda')  # Ensure it's on GPU
    
    # Benchmark the PyTorch model first
    pt_time, pt_fps = benchmark_model(pt_model, f"PyTorch model ({pt_model_path})")
    
    # Check if TensorRT model exists and if conversion is needed
    trt_exists = os.path.exists(trt_model_path)
    
    if not trt_exists or args.convert:
        if args.benchmark_only:
            print("\n⚠️ Warning: --benchmark-only flag is set but TensorRT model doesn't exist.")
            print("   Will perform conversion despite --benchmark-only flag.")
        
        # Convert to TensorRT
        print("\n🔧 Converting YOLO11l to TensorRT with optimized settings...")
        pt_model.export(
            format="engine",
            dynamic=False,      # Use static shape for better compatibility
            batch=1,            # Single batch for compatibility
            workspace=4,        # 4 GB Workspace
            half=True,          # Use FP16 precision
            device=0,           # Use GPU 0
            int8=False,         # Avoid INT8 quantization for better compatibility for Jetson series
            simplify=True,      # Simplify the model where possible
            imgsz=640,          # Match the image size used in scenegraph_online_v0.py
            nms=False,          # Enable NMS or disable it if you want to use it in the main application
            verbose=True        # Show detailed optimization info
        )
        print("\n✅ TensorRT conversion successful!")
    elif args.benchmark_only:
        print("\n📝 Skipping conversion as requested (--benchmark-only flag is set)")
    else:
        print(f"\n📝 TensorRT model ({trt_model_path}) already exists. Skipping conversion.")
        print("   Use --convert flag to force reconversion.")
    
    # Load and benchmark the TensorRT model
    print(f"\n📂 Loading TensorRT model ({trt_model_path})...")
    trt_model = YOLO(trt_model_path)
    trt_time, trt_fps = benchmark_model(trt_model, f"TensorRT model ({trt_model_path})")
    
    # Compare the results
    speedup = pt_time / trt_time
    print("\n📊 Performance Comparison:")
    print(f"   - PyTorch model: {pt_time*1000:.2f} ms, {pt_fps:.1f} FPS")
    print(f"   - TensorRT model: {trt_time*1000:.2f} ms, {trt_fps:.1f} FPS")
    print(f"   - Speedup: {speedup:.2f}x")
    
except Exception as e:
    print(f"\n❌ Error: {e}")
    print("The existing yolo11l.engine might still work in the main application.")
