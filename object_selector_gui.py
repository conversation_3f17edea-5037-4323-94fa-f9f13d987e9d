#!/usr/bin/env python3

"""
Object selection GUI for pAIrSEEption
Enables navigation to a detected object with the robot
"""

from PySide6.QtCore import Qt, Signal, QObject, QMetaObject, Q_ARG
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QComboBox, QGroupBox,
                              QSpinBox, QDoubleSpinBox, QCheckBox, QFormLayout)
import threading
import numpy as np
import time

try:
    from husky_controller import HuskyController
    ROS2_AVAILABLE = True
except ImportError:
    ROS2_AVAILABLE = False
    print("ROS2 not available - Navigation disabled")

class ObjectSelectorWidget(QWidget):
    """
    Widget for object selection and robot navigation
    Can be integrated into the existing pAIrSEEption GUI
    """
    
    # Signal for GUI updates
    status_update = Signal(str)
    
    def __init__(self, parent=None, tts_worker=None):
        super().__init__(parent)
        self.setWindowTitle("Object Navigation")

        # Internal data
        self.tracked_objects = {}  # ID -> object data
        self.selected_object_id = None

        # TTS worker reference for navigation announcements
        self.tts_worker = tts_worker

        # Initialize robot controller if ROS2 is available
        self.husky_controller = None
        if ROS2_AVAILABLE:
            try:
                self.husky_controller = HuskyController(tts_worker=tts_worker)
                print("Husky Controller successfully initialized")
            except Exception as e:
                print(f"Error initializing Husky Controller: {e}")

        # Build UI
        self._init_ui()

        # Last sent navigation command
        self.last_navigation_command = time.time() - 10.0  # Initial value for rate limiting
    
    def _init_ui(self):
        """Initialize UI elements"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Robot Navigation")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(title_label)
        
        # Selection area: Object class and ID
        selection_group = QGroupBox("Object Selection")
        selection_layout = QFormLayout()
        
        # Manual input
        id_layout = QHBoxLayout()
        self.class_input = QLineEdit("person")
        self.class_input.setPlaceholderText("Class (e.g. person)")
        id_layout.addWidget(self.class_input)
        
        self.id_input = QSpinBox()
        self.id_input.setRange(0, 99999)  # Support IDs up to 5 digits
        self.id_input.setValue(0)
        self.id_input.setPrefix("ID: ")
        self.id_input.setMinimumWidth(100)  # Make the input field wider
        id_layout.addWidget(self.id_input)
        selection_layout.addRow("Target object:", id_layout)
        
        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)
        
        # Navigation parameters
        nav_group = QGroupBox("Navigation Options")
        nav_layout = QFormLayout()
        
        self.target_distance = QDoubleSpinBox()
        self.target_distance.setRange(0.5, 5.0)
        self.target_distance.setValue(1.5)
        self.target_distance.setSingleStep(0.1)
        self.target_distance.setSuffix(" m")
        nav_layout.addRow("Target distance:", self.target_distance)
        
        # Add continuous tracking option
        self.continuous_tracking = QCheckBox("Continuous tracking")
        self.continuous_tracking.setChecked(True)
        self.continuous_tracking.setToolTip("Continuously update target position while navigating")
        nav_layout.addRow("", self.continuous_tracking)

        # Add follow mode option
        self.follow_mode = QCheckBox("Follow mode")
        self.follow_mode.setChecked(False)
        self.follow_mode.setToolTip("Continuously follow the person, maintaining distance even when they stop")
        nav_layout.addRow("", self.follow_mode)
        
        nav_group.setLayout(nav_layout)
        layout.addWidget(nav_group)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.navigate_button = QPushButton("Navigate to Object")
        self.navigate_button.clicked.connect(self._on_navigate_clicked)
        button_layout.addWidget(self.navigate_button)
        
        self.stop_button = QPushButton("Stop")
        self.stop_button.clicked.connect(self._on_stop_clicked)
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
        
        # Status display
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        self.status_update.connect(self.status_label.setText)
        
        # Enable/disable UI based on ROS2 availability
        self._update_ui_state()
        
        # Done
        layout.addStretch()
    
    def _update_ui_state(self):
        """Updates UI element activation/deactivation"""
        enabled = self.husky_controller is not None
        
        self.navigate_button.setEnabled(enabled)
        self.stop_button.setEnabled(enabled)
        
        if not enabled:
            self.status_label.setText("ROS2 not available - Navigation disabled")
    
    def _on_navigate_clicked(self):
        """Called when navigation button is clicked"""
        if self.husky_controller is None:
            self.status_update.emit("Error: ROS2/Husky Controller not available")
            return
        
        # Rate limiting (max once every 2 seconds)
        current_time = time.time()
        if current_time - self.last_navigation_command < 2.0:
            self.status_update.emit("Too many commands - please wait")
            return
        self.last_navigation_command = current_time
        
        # Get manually entered class and ID
        class_name = self.class_input.text().strip()
        obj_id = self.id_input.value()
        
        # Search for object with matching class and ID
        # Support both German and English class names
        target_object = None

        # Define mapping from German TTS names to YOLO class names
        german_to_yolo_mapping = {
            "stuhl": ["chair", "black chair"],
            "tisch": ["table", "dining table"],
            "auto": ["car"],
            "person": ["person"],
        }

        # Get possible YOLO class names for the input
        possible_class_names = [class_name.lower()]
        if class_name.lower() in german_to_yolo_mapping:
            possible_class_names.extend([name.lower() for name in german_to_yolo_mapping[class_name.lower()]])

        for obj_id_key, obj_data in self.tracked_objects.items():
            obj_class_name = obj_data.get('class_name', '').lower()
            if (obj_class_name in possible_class_names and obj_id_key == obj_id):
                target_object = obj_data
                break
        
        if target_object is None:
            error_message = f"Error: Object {class_name} with ID {obj_id} not found"
            self.status_update.emit(error_message)
            # TTS announcement for object not found
            if self.tts_worker:
                QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text", Qt.QueuedConnection, Q_ARG(str, "Ziel nicht gefunden!"))
            return
        
        # Check object position
        position = target_object.get('position')
        if not position or len(position) != 3:
            self.status_update.emit("Error: Object has no valid position")
            return
        
        # Store the ID of the object we're navigating to
        self.selected_object_id = obj_id
        
        # Navigate to object
        target_distance = self.target_distance.value()
        follow_mode = self.follow_mode.isChecked()

        try:
            # Get bounding box for image-based orientation
            bounding_box = target_object.get('bounding_box_2d')
            self.husky_controller.navigate_to_object(obj_id, position, stop_at_distance=target_distance, follow_mode=follow_mode, bounding_box=bounding_box)

            if follow_mode:
                success_message = f"Following {class_name} (ID: {obj_id}) started"
                tts_message = f"Folge {class_name} {obj_id}"
            else:
                success_message = f"Navigation to {class_name} (ID: {obj_id}) started"
                tts_message = f"Navigiere zu {class_name} {obj_id}"

            self.status_update.emit(success_message)
            # TTS announcement for navigation/follow start
            if self.tts_worker:
                QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text", Qt.QueuedConnection, Q_ARG(str, tts_message))
        except Exception as e:
            self.status_update.emit(f"Navigation error: {str(e)}")
    
    def _on_stop_clicked(self):
        """Stops navigation"""
        if self.husky_controller:
            self.husky_controller.stop()
            self.selected_object_id = None  # Clear tracking
            self.status_update.emit("Navigation stopped")
            # TTS announcement for navigation stopped
            if self.tts_worker:
                QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text", Qt.QueuedConnection, Q_ARG(str, "Navigation gestoppt!"))
    
    def update_objects(self, objects_data):
        """
        Updates the list of tracked objects and handles safety features
        
        Args:
            objects_data: List of object data from pAIrSEEption processing
        """
        if not objects_data:
            # No objects detected at all
            if self.husky_controller and self.selected_object_id is not None:
                # We were tracking an object but now there are none - stop for safety
                self.husky_controller.stop()
                self.status_update.emit("Navigation stopped: No objects detected")
                self.selected_object_id = None
            return
        
        # Update objects dictionary
        new_objects = {}
        for obj in objects_data:
            # Only objects with ID and position
            obj_id = obj.get('object_id')
            if obj_id is None:
                continue
            
            # Check position
            position = obj.get('position')
            if not position or len(position) != 3:
                continue
            
            # Store in dictionary
            new_objects[obj_id] = obj
        
        # Check if we're currently tracking an object and continuous tracking is enabled
        if (self.husky_controller and self.selected_object_id is not None and 
                self.continuous_tracking.isChecked()):
            if self.selected_object_id in new_objects:
                # Object still visible - update position and bounding box
                target_obj = new_objects[self.selected_object_id]
                self.husky_controller.update_target_position(
                    self.selected_object_id,
                    target_obj.get('position'),
                    target_obj.get('bounding_box_2d')  # Add bounding box for image-based orientation
                )
                # Optional: Update status occasionally
                current_time = time.time()
                if hasattr(self, 'last_status_update') and current_time - self.last_status_update > 3.0:
                    self.status_update.emit(f"Tracking object ID {self.selected_object_id}")
                    self.last_status_update = current_time
                elif not hasattr(self, 'last_status_update'):
                    self.last_status_update = current_time
            else:
                # Object no longer visible - stop for safety
                self.husky_controller.stop()
                self.status_update.emit(f"Navigation stopped: Object ID {self.selected_object_id} no longer detected")
                self.selected_object_id = None
        
        # Update tracked objects
        self.tracked_objects = new_objects
    
    def shutdown(self):
        """Clean up before program exit"""
        if self.husky_controller:
            self.husky_controller.stop()
            self.husky_controller.shutdown()

# Simple test if executed directly
if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    window = ObjectSelectorWidget()
    window.resize(400, 300)
    window.show()
    
    # Example objects for testing
    test_objects = [
        {
            'object_id': 1,
            'class_name': 'person',
            'position': [1.5, 0.3, 0.0],
            'confidence': 0.95
        },
        {
            'object_id': 2,
            'class_name': 'chair',
            'position': [2.0, -0.5, 0.0],
            'confidence': 0.88
        }
    ]
    window.update_objects(test_objects)
    
    sys.exit(app.exec()) 