import sys
import csv
import os
import threading
import time
import copy
import cv2
import numpy as np
from datetime import datetime
import pyzed.sl as sl # ZED SDK

# Initialize ZED constants after ZED SDK import
try:
    from core import initialize_zed_constants
    initialize_zed_constants()
except ImportError:
    print("⚠️  Core module not available for ZED constants initialization")

# Import worker classes from modularized modules
try:
    from perception import PerceptionWorker
    from ai import WhisperWorker, OpenRouterWorker, OllamaWorker
    WORKERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Worker modules not available: {e}")
    WORKERS_AVAILABLE = False

# System monitoring imports
try:
    import psutil  # For CPU/Memory monitoring
except ImportError:
    psutil = None

try:
    import pynvml  # NVIDIA GPU monitoring
    pynvml.nvmlInit()
except ImportError:
    pynvml = None

try:
    import subprocess  # For Jetson-specific tegrastats
except ImportError:
    subprocess = None

# Set PyTorch memory management for better GPU handling
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

try:
    import torch
except ImportError:
    torch = None 
from ultralytics import YOLO, YOLOE

# For Text-to-Speech
PYGAME_HIDE_SUPPORT_PROMPT=1
try:
    from TTS.api import TTS
except ImportError:
    print("TTS library not found. Please install it: pip install TTS")
    TTS = None

# For Voice Intent Recognition
import re

# Import configuration and constants from core module
from core import (
    OLLAMA_API_URL,
    AVAILABLE_YOLO_MODELS, DEFAULT_YOLO_MODEL, YOLO_MODEL_PATH,
    AVAILABLE_ONLINE_MODELS, DEFAULT_ONLINE_MODEL,
    AVAILABLE_OLLAMA_MODELS, DEFAULT_OLLAMA_MODEL,
    ZED_RESOLUTION, ZED_FPS, ZED_DEPTH_MODE, ZED_COORDINATE_SYSTEM,
    INITIAL_CONFIDENCE_THRESHOLD, ROBOT_CONTROL_AVAILABLE,
    WHISPER_MODEL_NAME,
    IMAGE_SAVE_DIR, ANNOTATED_IMAGE_SAVE_DIR, RESPONSE_LOG_DIR,
    OLLAMA_IMAGE_SAVE_DIR, OLLAMA_ANNOTATED_IMAGE_SAVE_DIR, OLLAMA_RESPONSE_LOG_DIR,
    OBJECT_LOG_CSV, DISTANCE_LOG_CSV, PERFORMANCE_LOG_CSV,
    ZED_DETECTION_CONFIDENCE_THRESHOLD, ZED_ENABLE_TRACKING, ZED_ENABLE_SEGMENTATION,
    initialize_zed_constants
)

# Import PySide6 modules
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QLabel, QPushButton,
                             QVBoxLayout, QHBoxLayout, QGridLayout, QSlider, QComboBox,
                             QCheckBox, QLineEdit, QTextEdit, QTextBrowser, QGroupBox,
                             QStackedWidget)
from PySide6.QtCore import Qt, QThread, Signal, Slot, QObject, QTimer, QMetaObject, Q_ARG
from PySide6.QtGui import QImage, QPixmap, QTextCursor

# Robot control integration
if ROBOT_CONTROL_AVAILABLE:
    try:
        from object_selector_gui import ObjectSelectorWidget
    except ImportError:
        ROBOT_CONTROL_AVAILABLE = False
        print("Robot control was enabled in config, but object_selector_gui.py could not be imported.")
else:
    print("Robot control disabled by configuration.")

# --- Voice Intent Recognition Classes ---
class VoiceIntentParser:
    """Parses German voice commands and extracts intents with English object mapping"""

    def __init__(self):
        # German to normalized object mapping for TTS and object matching
        # Maps German voice input to consistent object names for TTS
        self.object_translation = {
            "person": "person",
            "personen": "person",
            "menschen": "person",
            "mann": "person",
            "frau": "person",
            "stuhl": "stuhl",  # Keep German for TTS
            "stühle": "stuhl",
            "sessel": "stuhl",
            "tisch": "tisch",  # Keep German for TTS
            "auto": "auto",    # Keep German for TTS
            "wagen": "auto",
            "fahrzeug": "auto",
        }

        # Mapping from German TTS names to YOLO class names for object matching
        self.tts_to_yolo_mapping = {
            "person": ["person"],
            "stuhl": ["chair", "black chair"],  # Map German "stuhl" to English YOLO classes
            "tisch": ["table", "dining table"],
            "auto": ["car"],
        }

        # Intent patterns (German input) - Order matters! More specific patterns first
        self.patterns = [
            # System commands (most specific first)
            (r"(?:starte|start)\s+(?:wahrnehmung|perception)", self._parse_start_perception),
            (r"(?:stoppe|stop)\s+(?:wahrnehmung|perception)", self._parse_stop_perception),

            # Navigation patterns
            (r"(?:fahre|gehe|navigiere)\s+zu\s+(\w+)\s+(\d+)", self._parse_navigate),
            (r"(?:fahre|gehe|navigiere)\s+zu\s+(\w+)\s+nummer\s+(\d+)", self._parse_navigate),
            (r"(?:fahre|gehe|navigiere)\s+zu\s+(\w+)\s+id\s+(\d+)", self._parse_navigate),

            # Follow patterns
            (r"folge\s+(\w+)\s+(\d+)", self._parse_follow),
            (r"folge\s+(\w+)\s+nummer\s+(\d+)", self._parse_follow),
            (r"folge\s+(\w+)\s+id\s+(\d+)", self._parse_follow),
            (r"verfolge\s+(\w+)\s+(\d+)", self._parse_follow),

            # Distance setting - handle both "1,5" and "1 komma 5" formats
            (r"abstand\s+(\d+(?:[.,]\d+)?)\s*(?:meter|m)", self._parse_distance),
            (r"abstand\s+(\d+)\s+komma\s+(\d+)\s*(?:meter|m)", self._parse_distance_komma),
            (r"distanz\s+(\d+(?:[.,]\d+)?)\s*(?:meter|m)", self._parse_distance),
            (r"distanz\s+(\d+)\s+komma\s+(\d+)\s*(?:meter|m)", self._parse_distance_komma),

            # Scene analysis (including "Was siehst du" and "Was sehe ich")
            (r"(?:analysiere|beschreibe|zeige)\s+(?:szene|bild|scene)", self._parse_analyze),
            (r"(?:bild\s+analysieren|bildanalyse|szenenanalyse|szene\s+analysieren)", self._parse_analyze),
            (r"was\s+(?:siehst\s+du|sehe\s+ich|ist\s+(?:da|hier|zu\s+sehen))", self._parse_analyze),
            (r"was\s+(?:gibt\s+es\s+(?:hier|da)|ist\s+(?:im\s+bild|in\s+der\s+szene))", self._parse_analyze),

            # Mode toggles
            (r"folgemodus\s+(?:an|ein|on)", self._parse_follow_mode_on),
            (r"folgemodus\s+(?:aus|off)", self._parse_follow_mode_off),

            # General stop patterns (less specific, should be last)
            (r"(?:stopp|halt|anhalten|stop)", self._parse_stop),
        ]

    def parse(self, text):
        """Parse German voice input and return intent data"""
        text = text.lower().strip()

        # Clean up common transcription errors and normalize text
        text = self._normalize_text(text)

        for pattern, handler in self.patterns:
            match = re.search(pattern, text)
            if match:
                return handler(match)

        # Try fuzzy matching for navigation commands
        fuzzy_result = self._try_fuzzy_navigation_match(text)
        if fuzzy_result:
            return fuzzy_result

        # Fallback: Chat intent for unrecognized commands
        return {"intent": "chat", "text": text, "language": "german"}

    def _normalize_text(self, text):
        """Normalize text to handle common transcription errors"""
        # Remove audio ID prefix if present
        text = re.sub(r'\[audio id: [^\]]+\]\s*', '', text)

        # Convert to lowercase first for consistent processing
        text = text.lower()

        # Handle common transcription errors for "fahre"
        text = re.sub(r'\bfahrre\b', 'fahre', text)
        text = re.sub(r'\bfahrer\b', 'fahre', text)
        text = re.sub(r'\bfarre\b', 'fahre', text)
        text = re.sub(r'\bfare\b', 'fahre', text)

        # Handle common transcription errors for "folge"
        text = re.sub(r'\bvolge\b', 'folge', text)
        text = re.sub(r'\bfolgen\b', 'folge', text)

        # Normalize object names (already lowercase)
        text = re.sub(r'\bperson\b', 'person', text)
        text = re.sub(r'\bstuhl\b', 'stuhl', text)
        text = re.sub(r'\btisch\b', 'tisch', text)

        # Convert written numbers to digits
        number_words = {
            "null": "0", "eins": "1", "zwei": "2", "drei": "3", "vier": "4",
            "fünf": "5", "sechs": "6", "sieben": "7", "acht": "8", "neun": "9",
            "zehn": "10", "elf": "11", "zwölf": "12"
        }

        for word, digit in number_words.items():
            text = re.sub(r'\b' + word + r'\b', digit, text)

        # Clean up extra spaces and punctuation
        text = re.sub(r'[.,!?]+$', '', text)  # Remove trailing punctuation
        text = re.sub(r'\s+', ' ', text)      # Normalize spaces

        return text.strip()

    def _translate_object(self, german_object):
        """Translate German object name to English"""
        return self.object_translation.get(german_object.lower(), german_object)

    def _parse_navigate(self, match):
        german_object = match.group(1)
        object_id = int(match.group(2))
        english_object = self._translate_object(german_object)
        return {
            "intent": "navigate",
            "object": english_object,
            "id": object_id,
            "original_object": german_object
        }

    def _parse_follow(self, match):
        german_object = match.group(1)
        object_id = int(match.group(2))
        english_object = self._translate_object(german_object)
        return {
            "intent": "follow",
            "object": english_object,
            "id": object_id,
            "original_object": german_object
        }

    def _parse_stop(self, match):
        return {"intent": "stop"}

    def _parse_distance(self, match):
        distance_str = match.group(1).replace(',', '.')
        distance = float(distance_str)
        return {"intent": "set_distance", "distance": distance}

    def _parse_distance_komma(self, match):
        # Handle "1 komma 5" format
        whole_part = match.group(1)
        decimal_part = match.group(2)
        distance_str = f"{whole_part}.{decimal_part}"
        distance = float(distance_str)
        return {"intent": "set_distance", "distance": distance}

    def _parse_analyze(self, match):
        return {"intent": "analyze_scene"}

    def _parse_start_perception(self, match):
        return {"intent": "start_perception"}

    def _parse_stop_perception(self, match):
        return {"intent": "stop_perception"}

    def _parse_follow_mode_on(self, match):
        return {"intent": "toggle_follow_mode", "state": True}

    def _parse_follow_mode_off(self, match):
        return {"intent": "toggle_follow_mode", "state": False}

    def _try_fuzzy_navigation_match(self, text):
        """Try to find navigation commands with fuzzy matching"""
        import difflib

        # Common navigation patterns to match against
        navigation_templates = [
            "fahre zu person {id}",
            "gehe zu person {id}",
            "navigiere zu person {id}",
            "fahre zu stuhl {id}",
            "gehe zu stuhl {id}",
            "navigiere zu stuhl {id}",
            "fahre zu tisch {id}",
            "gehe zu tisch {id}",
            "navigiere zu tisch {id}",
        ]

        # Extract potential ID from text
        import re
        id_match = re.search(r'\b(\d+)\b', text)
        if not id_match:
            return None

        object_id = int(id_match.group(1))

        # Try to match against templates
        best_match = None
        best_ratio = 0.0

        for template in navigation_templates:
            # Replace {id} with the actual ID found
            filled_template = template.replace("{id}", str(object_id))

            # Calculate similarity
            ratio = difflib.SequenceMatcher(None, text, filled_template).ratio()

            if ratio > best_ratio and ratio > 0.6:  # Minimum 60% similarity
                best_ratio = ratio
                best_match = filled_template

        if best_match:
            # Extract object type from the best match
            for obj_type in ["person", "stuhl", "tisch"]:
                if obj_type in best_match:
                    english_object = self._translate_object(obj_type)
                    return {
                        "intent": "navigate",
                        "object": english_object,
                        "id": object_id,
                        "original_object": obj_type,
                        "is_suggestion": True,
                        "original_text": text,
                        "confidence": best_ratio
                    }

        return None


class VoiceIntentExecutor:
    """Executes parsed voice intents by simulating GUI interactions"""

    def __init__(self, main_window):
        self.main_window = main_window
        self.object_selector = None  # Will be set when available

        # German response templates with enhanced feedback
        self.response_templates = {
            "navigate": "Navigation zu {original_object} {id} gestartet",
            "follow": "Folge {original_object} {id} aktiviert",
            "stop": "Navigation gestoppt",
            "set_distance": "Abstand auf {distance} Meter gesetzt",
            "analyze_scene": "Analysiere aktuelle Szene",
            "start_perception": "Wahrnehmung gestartet",
            "stop_perception": "Wahrnehmung gestoppt",
            "toggle_follow_mode": "Folgemodus {state}",
            "error": "Fehler: {message}",
            "unknown": "Kommando nicht erkannt: {text}",
            "object_not_found": "Objekt {original_object} {id} nicht gefunden",
            "navigation_active": "Navigation bereits aktiv zu {original_object} {id}",
            "no_navigation": "Keine aktive Navigation zum Stoppen",
            "perception_already_running": "Wahrnehmung läuft bereits",
            "perception_already_stopped": "Wahrnehmung ist bereits gestoppt"
        }

    def format_for_tts(self, text):
        """Format text for better TTS pronunciation (same as LLM responses)"""
        # Replace decimal separators for better TTS pronunciation
        text = re.sub(r'(\d+)[.,](\d+)', r'\1 Komma \2', text),

        # Replace units for better pronunciation
        text = text.replace('m²', 'Quadratmeter')
        text = text.replace('m³', 'Kubikmeter')
        text = text.replace('km/h', 'Kilometer pro Stunde')
        text = text.replace('m/s', 'Meter pro Sekunde')

        return text

    def set_object_selector(self, object_selector):
        """Set reference to object selector widget"""
        self.object_selector = object_selector

    def execute(self, intent_data):
        """Execute the parsed intent and return response message"""
        intent = intent_data["intent"]

        try:
            if intent == "navigate":
                return self._execute_navigate(intent_data)
            elif intent == "follow":
                return self._execute_follow(intent_data)
            elif intent == "stop":
                return self._execute_stop()
            elif intent == "set_distance":
                return self._execute_set_distance(intent_data)
            elif intent == "analyze_scene":
                return self._execute_analyze_scene()
            elif intent == "start_perception":
                return self._execute_start_perception()
            elif intent == "stop_perception":
                return self._execute_stop_perception()
            elif intent == "toggle_follow_mode":
                return self._execute_toggle_follow_mode(intent_data)
            elif intent == "chat":
                # Chat will be handled separately
                return None
            else:
                return self.response_templates["unknown"].format(text=intent_data.get("text", ""))

        except Exception as e:
            return self.response_templates["error"].format(message=str(e))

    def _execute_navigate(self, data):
        if not self.object_selector:
            raise Exception("Robot navigation nicht verfügbar")

        # Check if this is a suggested command (from fuzzy matching)
        if data.get("is_suggestion", False):
            # This is a suggestion - store it for confirmation
            if hasattr(self.main_window, 'conversation_context'):
                original_text = data.get("original_text", "")
                suggested_command = f"Fahre zu {data['original_object']} {data['id']}"
                self.main_window.conversation_context.set_pending_confirmation(data, suggested_command)
                return f"Meinten Sie: {suggested_command}?"
            else:
                # Fallback if no conversation context
                return f"Meinten Sie: Fahre zu {data['original_object']} {data['id']}?"

        # Normal navigation execution
        self.object_selector.class_input.setText(data["object"])
        self.object_selector.id_input.setValue(data["id"])
        self.object_selector.follow_mode.setChecked(False)
        self.object_selector._on_navigate_clicked()

        # Return None to prevent duplicate TTS - object_selector already handles TTS
        return None

    def _execute_follow(self, data):
        if not self.object_selector:
            raise Exception("Robot navigation nicht verfügbar")

        # Set GUI values and trigger follow mode
        self.object_selector.class_input.setText(data["object"])
        self.object_selector.id_input.setValue(data["id"])
        self.object_selector.follow_mode.setChecked(True)
        self.object_selector._on_navigate_clicked()

        # Return None to prevent duplicate TTS - object_selector already handles TTS
        return None

    def _execute_stop(self):
        if not self.object_selector:
            raise Exception("Robot navigation nicht verfügbar")

        self.object_selector._on_stop_clicked()
        # Return None to prevent duplicate TTS - object_selector already handles TTS
        return None

    def _execute_set_distance(self, data):
        if not self.object_selector:
            raise Exception("Robot navigation nicht verfügbar")

        self.object_selector.target_distance.setValue(data["distance"])
        response = self.response_templates["set_distance"].format(distance=data["distance"])
        # Format for better TTS pronunciation
        return self.format_for_tts(response)

    def _execute_analyze_scene(self):
        self.main_window.send_scene_to_ai()
        return self.response_templates["analyze_scene"]

    def _execute_start_perception(self):
        if not self.main_window.perception_running:
            self.main_window.toggle_perception()
            return self.response_templates["start_perception"]
        else:
            return self.response_templates["perception_already_running"]

    def _execute_stop_perception(self):
        if self.main_window.perception_running:
            self.main_window.toggle_perception()
            return self.response_templates["stop_perception"]
        else:
            return self.response_templates["perception_already_stopped"]

    def _execute_toggle_follow_mode(self, data):
        if not self.object_selector:
            raise Exception("Robot navigation nicht verfügbar")

        state = data["state"]
        self.object_selector.follow_mode.setChecked(state)
        state_text = "aktiviert" if state else "deaktiviert"
        return self.response_templates["toggle_follow_mode"].format(state=state_text)


class ConversationContext:
    """Manages conversation context and memory for voice interactions"""

    def __init__(self, max_history=10):
        self.history = []  # List of interaction dictionaries
        self.current_topic = None  # Current conversation topic
        self.active_navigation = None  # Current navigation state
        self.last_mentioned_objects = {}  # Recently mentioned objects
        self.max_history = max_history

        # Confirmation system for "Meinten Sie..." questions
        self.pending_confirmation = None  # Stores the suggested command for confirmation
        self.last_suggestion = None  # Last suggested command text
        self.suggestion_timestamp = None  # When the suggestion was made

    def add_interaction(self, user_input, bot_response, intent_data):
        """Add a new interaction to the conversation history"""
        interaction = {
            "timestamp": time.time(),
            "user": user_input,
            "bot": bot_response,
            "intent": intent_data,
        }

        self.history.append(interaction)
        if len(self.history) > self.max_history:
            self.history.pop(0)

        # Update context based on the interaction
        self._update_context(intent_data)

    def _update_context(self, intent_data):
        """Update conversation context based on intent data"""
        intent = intent_data["intent"]

        if intent in ["navigate", "follow"]:
            self.current_topic = "navigation"
            self.active_navigation = {
                "object": intent_data.get("object"),
                "id": intent_data.get("id"),
                "mode": intent,
                "original_object": intent_data.get("original_object")
            }
            # Remember this object
            if "object" in intent_data and "id" in intent_data:
                obj_key = f"{intent_data['object']}_{intent_data['id']}"
                self.last_mentioned_objects[obj_key] = {
                    "object": intent_data["object"],
                    "id": intent_data["id"],
                    "original_object": intent_data.get("original_object"),
                    "timestamp": time.time()
                }

        elif intent == "stop":
            if self.active_navigation:
                self.current_topic = "navigation_stopped"
            self.active_navigation = None

        elif intent == "chat":
            # Analyze chat topic
            text = intent_data.get("text", "").lower()
            if "wetter" in text:
                self.current_topic = "weather"
            elif "zeit" in text or "uhr" in text:
                self.current_topic = "time"
            elif "navigation" in text or "roboter" in text:
                self.current_topic = "robot_discussion"
            else:
                self.current_topic = "general_chat"

    def resolve_references(self, text):
        """Resolve references like 'das', 'sie', 'es', 'dort' in German text"""
        text_lower = text.lower()
        resolved_text = text

        # Resolve navigation references (only for navigation-related contexts)
        if self.active_navigation and any(word in text_lower for word in ["navigation", "stopp", "halt", "anhalten", "das", "dort"]):
            nav = self.active_navigation
            object_ref = f"{nav['original_object']} {nav['id']}"

            # Replace navigation-specific references
            if "stopp" in text_lower or "halt" in text_lower or "anhalten" in text_lower:
                resolved_text = resolved_text.replace("das", f"die Navigation zu {object_ref}")

            # Only replace pronouns in navigation context
            if any(word in text_lower for word in ["navigation", "roboter", "fahrt"]):
                resolved_text = resolved_text.replace("sie", f"{nav['original_object']} {nav['id']}")
                resolved_text = resolved_text.replace("es", f"{nav['original_object']} {nav['id']}")
                resolved_text = resolved_text.replace("dort", f"zu {object_ref}")
                resolved_text = resolved_text.replace("dorthin", f"zu {object_ref}")

        # Resolve object references from recent mentions
        if "letzte" in text_lower or "vorherige" in text_lower:
            if self.last_mentioned_objects:
                # Check if we're looking for a specific object type
                if "person" in text_lower:
                    # Find most recent person
                    person_objects = {k: v for k, v in self.last_mentioned_objects.items()
                                    if v["object"] == "person"}
                    if person_objects:
                        latest_obj = max(person_objects.values(), key=lambda x: x["timestamp"])
                        obj_ref = f"{latest_obj['original_object']} {latest_obj['id']}"
                        resolved_text = resolved_text.replace("letzten person", obj_ref)
                        resolved_text = resolved_text.replace("letzte person", obj_ref)
                elif "stuhl" in text_lower or "chair" in text_lower:
                    # Find most recent chair
                    chair_objects = {k: v for k, v in self.last_mentioned_objects.items()
                                   if v["object"] == "chair"}
                    if chair_objects:
                        latest_obj = max(chair_objects.values(), key=lambda x: x["timestamp"])
                        obj_ref = f"{latest_obj['original_object']} {latest_obj['id']}"
                        resolved_text = resolved_text.replace("letzten stuhl", obj_ref)
                        resolved_text = resolved_text.replace("letzte stuhl", obj_ref)
                else:
                    # Get most recent object of any type
                    latest_obj = max(self.last_mentioned_objects.values(),
                                   key=lambda x: x["timestamp"])
                    obj_ref = f"{latest_obj['original_object']} {latest_obj['id']}"
                    resolved_text = resolved_text.replace("letzte", obj_ref)
                    resolved_text = resolved_text.replace("vorherige", obj_ref)

        return resolved_text

    def get_context_info(self):
        """Get current context information for debugging"""
        return {
            "current_topic": self.current_topic,
            "active_navigation": self.active_navigation,
            "recent_objects": list(self.last_mentioned_objects.keys()),
            "history_length": len(self.history)
        }

    def set_pending_confirmation(self, suggested_command, original_text):
        """Set a command that's waiting for user confirmation"""
        self.pending_confirmation = suggested_command
        self.last_suggestion = original_text
        self.suggestion_timestamp = time.time()

    def get_pending_confirmation(self):
        """Get the command waiting for confirmation, if still valid"""
        if self.pending_confirmation and self.suggestion_timestamp:
            # Confirmation expires after 30 seconds
            if time.time() - self.suggestion_timestamp < 30:
                return self.pending_confirmation
            else:
                # Expired, clear it
                self.clear_pending_confirmation()
        return None

    def clear_pending_confirmation(self):
        """Clear any pending confirmation"""
        self.pending_confirmation = None
        self.last_suggestion = None
        self.suggestion_timestamp = None

    def is_confirmation_response(self, text):
        """Check if the text is a confirmation response (yes/no)"""
        text_lower = text.lower().strip()

        # German yes responses
        yes_responses = ["ja", "yes", "richtig", "korrekt", "genau", "stimmt", "ok", "okay"]
        # German no responses
        no_responses = ["nein", "no", "falsch", "nicht", "nee", "nö"]

        return text_lower in yes_responses or text_lower in no_responses

    def is_positive_confirmation(self, text):
        """Check if the text is a positive confirmation (yes)"""
        text_lower = text.lower().strip()
        yes_responses = ["ja", "yes", "richtig", "korrekt", "genau", "stimmt", "ok", "okay"]
        return text_lower in yes_responses

    def clear_context(self):
        """Clear conversation context"""
        self.history.clear()
        self.current_topic = None
        self.active_navigation = None
        self.last_mentioned_objects.clear()
        self.clear_pending_confirmation()


# --- Helper Functions ---
def zed_box_to_xyxy(zed_box_2d):
    if not isinstance(zed_box_2d, np.ndarray):
        zed_box_2d = np.array(zed_box_2d)
    if zed_box_2d.shape != (4, 2):
        return None
    x_coords = zed_box_2d[:, 0]
    y_coords = zed_box_2d[:, 1]
    return [np.min(x_coords), np.min(y_coords), np.max(x_coords), np.max(y_coords)]

def generate_request_id():
    """Generate a unique request ID using timestamp and random component"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    random_component = ''.join([str(np.random.randint(0, 10)) for _ in range(4)])
    return f"{timestamp}_{random_component}"

# --- Text Prompt for YOLOE ---
CLASS_NAMES = [
    "person", "black chair"
]

# --- Define CSV Headers ---
PERFORMANCE_LOG_HEADER = ['timestamp', 'frame_id', 'fps', 'objects', 'active_objects', 'cpu_percent', 'gpu_percent', 'memory_percent', 'gpu_memory_percent']
OBJECT_LOG_HEADER = ['timestamp','frame_id','object_id','label','class_name','confidence','position_x','position_y','position_z','velocity_x','velocity_y','velocity_z','speed_ms','speed_kmh','speed_2d_ms','speed_2d_kmh','speed_filtered_ms','speed_filtered_kmh','speed_2d_filtered_ms','speed_2d_filtered_kmh','is_static','smoothed_speed','bounding_box_2d','mask_available']
DISTANCE_LOG_HEADER = ['timestamp','frame_id','object_id_1','object_id_2','distance']

# --- Function to initialize CSV files ---
def initialize_csv(filepath, header):
    write_header = not os.path.exists(filepath)
    try:
        with open(filepath, 'a', newline='') as csvfile:
            writer = csv.writer(csvfile)
            if write_header:
                writer.writerow(header)
    except IOError as e:
        print(f"Error accessing CSV {filepath}: {e}")

# --- Perception Worker (moved to perception/zed_worker.py) ---

    def __init__(self):
        super().__init__()
        self._running = False
        self._lock = threading.Lock()
        self._current_conf_threshold = INITIAL_CONFIDENCE_THRESHOLD
        self._threshold_lock = threading.Lock()
        self._current_model_path = YOLO_MODEL_PATH
        self._model_path_lock = threading.Lock()
        self._model_reload_requested = False
        self._model_reload_lock = threading.Lock()
        self.zed = None
        self.yolo_model = None
        self.init_params = None
        self.runtime_params = None
        self.obj_det_params = None
        self.obj_det_runtime_params = None
        self.left_image = sl.Mat()
        self.objects = sl.Objects()
        self.latest_object_states = []
        self.latest_states_lock = threading.Lock()
        self.current_frame_sl_masks = []
        self._display_options_lock = threading.Lock()
        self._show_yolo_plot_bboxes_labels_conf = True
        self._show_yolo_plot_masks = True
        self._show_zed_id = True
        self._show_zed_pos = True
        self._show_zed_speed = True
        self._log_performance = False  # Flag to control performance logging
        self.is_yoloe_model = False  # Flag to indicate if current model is YOLOE

        # Add timeout tracking for ghosting prevention
        self.object_last_seen = {}  # Track when objects were last seen
        self.object_timeout = 1.5   # Reduced timeout to 1.5 seconds for faster cleanup

        # Speed filtering for static object detection (much higher thresholds for ZED noise)
        self.speed_threshold_static = 0.25  # Objects moving slower than 0.25 m/s (25 cm/s) are considered static
        self.speed_threshold_very_static = 0.20  # Higher threshold for furniture (20 cm/s)
        self.speed_history = {}  # Track speed history for smoothing
        self.speed_history_length = 10  # Increased to 10 frames for better smoothing

        # Position smoothing removed - all objects treated equally

        # Universal static threshold for ALL objects (no object-specific treatment)
        # All objects use the same threshold for fair treatment

        # Enhanced ghosting prevention
        self.object_confidence_history = {}  # Track confidence over time
        self.min_confidence_frames = 3  # Minimum frames with good confidence before accepting object

        # Statistics tracking
        self.stats_frame_times = []  # Track frame processing times for FPS calculation
        self.stats_max_frame_history = 30  # Keep last 30 frame times for rolling FPS average

        # System monitoring
        self.gpu_handle = None
        self.is_jetson = self._detect_jetson_platform()
        self._init_gpu_monitoring()

    def _detect_jetson_platform(self):
        """Detect if running on Jetson platform"""
        try:
            with open('/proc/device-tree/model', 'r') as f:
                model = f.read().lower()
                return 'jetson' in model or 'nvidia' in model
        except:
            # Alternative detection methods
            try:
                result = subprocess.run(['uname', '-a'], capture_output=True, text=True, timeout=2)
                return 'tegra' in result.stdout.lower() or 'aarch64' in result.stdout.lower()
            except:
                return False

    def _init_gpu_monitoring(self):
        """Initialize GPU monitoring based on platform"""
        if pynvml is not None:
            try:
                device_count = pynvml.nvmlDeviceGetCount()
                if device_count > 0:
                    self.gpu_handle = pynvml.nvmlDeviceGetHandleByIndex(0)  # Use first GPU
                    self.status_update.emit("Worker: GPU monitoring initialized (NVIDIA)")
                else:
                    self.status_update.emit("Worker: No NVIDIA GPU detected")
            except Exception as e:
                self.status_update.emit(f"Worker Warning: GPU monitoring init failed: {e}")
        else:
            self.status_update.emit("Worker: pynvml not available - GPU monitoring disabled")

    def _get_system_stats(self):
        """Get current system statistics"""
        stats = {'cpu_percent': 0.0, 'gpu_percent': 0.0, 'memory_percent': 0.0, 'gpu_memory_percent': 0.0}
        
        # CPU usage
        if psutil is not None:
            try:
                stats['cpu_percent'] = psutil.cpu_percent(interval=0)  # Non-blocking
                stats['memory_percent'] = psutil.virtual_memory().percent
            except Exception as e:
                self.status_update.emit(f"Worker Warning: CPU stats error: {e}")
        
        # GPU usage
        if self.gpu_handle is not None and pynvml is not None:
            try:
                # GPU utilization
                gpu_util = pynvml.nvmlDeviceGetUtilizationRates(self.gpu_handle)
                stats['gpu_percent'] = gpu_util.gpu
                
                # GPU memory
                gpu_mem = pynvml.nvmlDeviceGetMemoryInfo(self.gpu_handle)
                stats['gpu_memory_percent'] = (gpu_mem.used / gpu_mem.total) * 100
            except Exception as e:
                # Fallback for Jetson or other platforms
                if self.is_jetson:
                    stats.update(self._get_jetson_gpu_stats())
        elif self.is_jetson:
            # Use Jetson-specific monitoring
            stats.update(self._get_jetson_gpu_stats())
        
        return stats

    def _get_jetson_gpu_stats(self):
        """Get GPU stats specifically for Jetson platforms using tegrastats and sysfs"""
        stats = {'gpu_percent': 0.0, 'gpu_memory_percent': 0.0}
        try:
            # Try to read GPU utilization from sysfs
            gpu_freq_path = '/sys/devices/gpu.0/load'
            if os.path.exists(gpu_freq_path):
                with open(gpu_freq_path, 'r') as f:
                    load = int(f.read().strip())
                    stats['gpu_percent'] = (load / 1000.0) * 100  # Convert to percentage
            
            # Try to get GPU memory usage using tegrastats
            try:
                # Run tegrastats once to get memory info
                import subprocess
                result = subprocess.run(['tegrastats', '--interval', '1', '--count', '1'], 
                                       capture_output=True, text=True, timeout=2)
                
                # Parse the output for RAM and GPU RAM info
                if result.stdout:
                    # Example output: RAM 2586/7846MB (lfb 713x4MB) SWAP 0/3923MB (cached 0MB) CPU [0%@960,0%@960,0%@960,0%@960] EMC_FREQ 0% GR3D_FREQ 0% PLL@38.5C CPU@39C PMIC@100C GPU@38.5C AO@41.5C thermal@38.75C
                    output = result.stdout.strip()
                    
                    # Parse GPU memory if available
                    if 'GR3D_FREQ' in output:
                        gr3d_parts = output.split('GR3D_FREQ')[1].split('%')[0].strip()
                        if gr3d_parts.isdigit():
                            stats['gpu_percent'] = float(gr3d_parts)
                    
                    # Parse RAM usage for GPU memory approximation
                    if 'RAM' in output:
                        ram_parts = output.split('RAM')[1].split('MB')[0].strip().split('/')
                        if len(ram_parts) == 2:
                            used = float(ram_parts[0])
                            total = float(ram_parts[1])
                            if total > 0:
                                stats['gpu_memory_percent'] = (used / total) * 100
            except Exception as tegra_err:
                self.status_update.emit(f"Worker: Tegrastats error: {tegra_err}")
                
            # Alternative approach: try to read GPU memory from nvidia-smi if available
            if stats['gpu_memory_percent'] == 0.0:
                try:
                    result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total', '--format=csv,noheader,nounits'], 
                                           capture_output=True, text=True, timeout=2)
                    if result.stdout:
                        parts = result.stdout.strip().split(',')
                        if len(parts) == 2:
                            used = float(parts[0].strip())
                            total = float(parts[1].strip())
                            if total > 0:
                                stats['gpu_memory_percent'] = (used / total) * 100
                except Exception:
                    pass  # Silently fail if nvidia-smi is not available
            
            # If we still don't have GPU stats, try one more approach with specific Jetson files
            if stats['gpu_percent'] == 0.0:
                # Try reading GPU load from another possible location
                alt_paths = [
                    '/sys/devices/platform/host1x/gpu.0/load',
                    '/sys/kernel/debug/tegra_gpu/gpu_load'
                ]
                for path in alt_paths:
                    if os.path.exists(path):
                        try:
                            with open(path, 'r') as f:
                                load = int(f.read().strip())
                                stats['gpu_percent'] = (load / 1000.0) * 100  # Convert to percentage
                                break
                        except:
                            pass
        
        except Exception as e:
            self.status_update.emit(f"Worker: Jetson GPU stats error: {e}")
        
        return stats

    def _is_running(self):
        with self._lock:
            return self._running

    @Slot(float)
    def set_confidence_threshold(self, value):
        with self._threshold_lock:
            self._current_conf_threshold = value

    @Slot(bool)
    def set_show_yolo_plot_bboxes_labels_conf(self, show):
        with self._display_options_lock: self._show_yolo_plot_bboxes_labels_conf = show

    @Slot(bool)
    def set_show_yolo_plot_masks(self, show):
        with self._display_options_lock: self._show_yolo_plot_masks = show

    @Slot(bool)
    def set_show_zed_id(self, show):
        with self._display_options_lock: self._show_zed_id = show

    @Slot(bool)
    def set_show_zed_pos(self, show):
        with self._display_options_lock: self._show_zed_pos = show

    @Slot(bool)
    def set_show_zed_speed(self, show):
        with self._display_options_lock: self._show_zed_speed = show
        
    @Slot(bool)
    def set_log_performance(self, enabled):
        """Enable or disable performance logging to CSV"""
        with self._display_options_lock: self._log_performance = enabled
    
    @Slot(list)
    def update_yoloe_classes(self, class_names):
        """Update YOLOe model with new class names"""
        if not self.is_yoloe_model:
            self.status_update.emit("Worker: Cannot update classes - current model is not a YOLOe model")
            return
            
        self._apply_yoloe_classes(self.yolo_model, class_names)

    @Slot(str)
    def change_model(self, model_path):
        with self._model_path_lock:
            if self._current_model_path != model_path:
                self._current_model_path = model_path
                self.status_update.emit(f"Worker: Model change requested to {model_path}")
                # Set flag for model reload instead of doing it immediately
                with self._model_reload_lock:
                    self._model_reload_requested = True

    def _check_and_reload_model(self):
        """Check if model reload is requested and perform it safely"""
        with self._model_reload_lock:
            if not self._model_reload_requested:
                return
            self._model_reload_requested = False
        
        try:
            with self._model_path_lock:
                current_model_path = self._current_model_path
            
            self.status_update.emit(f"Worker: Reloading YOLO model to {current_model_path}...")
            
            # Determine task based on model name and file extension
            task = 'segment' if ('-seg' in current_model_path or 'yoloe' in current_model_path) else 'detect'
            if task != 'segment' and not current_model_path.endswith('.engine'):
                self.status_update.emit(f"Worker Warning: YOLO model {current_model_path} might not be a segmentation model.")
            
            # Check if this is a YOLOE model
            is_yoloe_model = 'yoloe' in current_model_path.lower()
            
            # Special handling for TensorRT .engine models
            if current_model_path.endswith('.engine'):
                self.status_update.emit(f"Worker: Loading TensorRT .engine model ({current_model_path})...")
                new_model = YOLO(current_model_path, task='detect')  # TensorRT models typically use detect task
                self.status_update.emit(f"Worker: TensorRT model ({current_model_path}) loaded successfully.")
            # Check if this is a YOLOE model and use YOLOE class instead of YOLO
            elif is_yoloe_model:
                new_model = YOLOE(current_model_path, task=task)
                self.status_update.emit(f"Worker: YOLOE model ({current_model_path}) loaded with task: {task}.")
            else:
                new_model = YOLO(current_model_path, task=task)
                self.status_update.emit(f"Worker: YOLO model ({current_model_path}) loaded with task: {task}.")
            
            # Only move to CUDA if not already a TensorRT model (TensorRT models are pre-optimized)
            if not current_model_path.endswith('.engine'):
                new_model.to('cuda')
                self.status_update.emit(f"Worker: Model moved to CUDA.")
            else:
                self.status_update.emit(f"Worker: TensorRT model already optimized for GPU.")
            
            # Apply YOLOE text prompts ONLY if this is a YOLOE model (but not prompt-free version)
            if is_yoloe_model and '-pf' not in current_model_path.lower():
                self._apply_yoloe_classes(new_model, CLASS_NAMES)
            elif is_yoloe_model:
                self.status_update.emit("Worker: Using YOLOE prompt-free model - no text prompts needed.")
            else:
                # Standard YOLO models don't need explicit class setting
                self.status_update.emit("Worker: Standard YOLO model loaded - no class setting needed.")
            
            # Replace old model
            old_model = self.yolo_model
            self.yolo_model = new_model
            self.is_yoloe_model = is_yoloe_model  # Store model type for later use
            
            # Clean up old model
            if old_model is not None:
                del old_model
                torch.cuda.empty_cache() if torch is not None else None
            
            self.status_update.emit(f"Worker: YOLO model reloaded successfully to {current_model_path}")
            self.model_changed.emit(current_model_path)
            
        except Exception as e:
            self.status_update.emit(f"Worker: Failed to reload YOLO model: {e}")
            # Reset the flag on failure
            with self._model_reload_lock:
                self._model_reload_requested = False

    def _apply_yoloe_classes(self, model, class_names):
        """Apply class names to YOLOe model"""
        self.status_update.emit("Worker: Applying YOLOE set_classes...")
        try:
            # Filter out empty class names
            valid_class_names = [name for name in class_names if name.strip()]
            if not valid_class_names:
                self.status_update.emit("Worker Warning: No valid class names provided")
                return
                
            # Check if the model has the necessary methods for text prompts
            if hasattr(model, 'set_classes') and hasattr(model, 'get_text_pe'):
                try:
                    # Get embeddings for all classes at once to ensure proper shape
                    embeddings = model.get_text_pe(valid_class_names)
                    
                    # Apply the classes and embeddings
                    model.set_classes(valid_class_names, embeddings)
                    self.status_update.emit(f"Worker: YOLOE set_classes completed with embeddings for {len(valid_class_names)} classes.")
                except Exception as e:
                    self.status_update.emit(f"Worker Error: Failed to process embeddings: {e}")
                    # Try without embeddings as fallback
                    try:
                        model.set_classes(valid_class_names)
                        self.status_update.emit("Worker: YOLOE set_classes completed (without embeddings).")
                    except Exception as e2:
                        self.status_update.emit(f"Worker Error: Failed fallback set_classes: {e2}")
            elif hasattr(model, 'set_classes'):
                # Fallback if get_text_pe is not available
                self.status_update.emit("Worker Warning: YOLOE get_text_pe not found, trying direct set_classes.")
                try:
                    model.set_classes(valid_class_names)
                    self.status_update.emit("Worker: YOLOE set_classes completed (without embeddings).")
                except TypeError:
                    self.status_update.emit("Worker Warning: YOLOE set_classes requires embeddings but get_text_pe not available.")
            else:
                self.status_update.emit("Worker Warning: YOLOE model does not support set_classes method.")
        except AttributeError as e:
            self.status_update.emit(f"Worker Warning: YOLOE method missing: {e}")
        except Exception as e_setup:
            self.status_update.emit(f"Worker Warning: YOLOE set_classes failed: {e_setup}")

    def initialize_resources(self):
        self.status_update.emit(f"Worker: Initializing resources...")
        try:
            # Initialize performance stats CSV file if needed
            # (Will only be used when logging is enabled via checkbox)
            if not os.path.exists(PERFORMANCE_LOG_CSV):
                initialize_csv(PERFORMANCE_LOG_CSV, PERFORMANCE_LOG_HEADER)
                self.status_update.emit(f"Worker: Performance stats CSV initialized: {PERFORMANCE_LOG_CSV}")
            
            with self._model_path_lock:
                current_model_path = self._current_model_path
            
            self.status_update.emit(f"Worker: Loading model {current_model_path}...")
            
            # Determine task based on model name and file extension
            task = 'segment' if ('-seg' in current_model_path or 'yoloe' in current_model_path) else 'detect'
            if task != 'segment' and not current_model_path.endswith('.engine'):
                 self.status_update.emit(f"Worker Warning: YOLO model {current_model_path} might not be a segmentation model. Masks are required for CustomMaskObjectData.")
            
            # Check if this is a YOLOE model
            self.is_yoloe_model = 'yoloe' in current_model_path.lower()
            
            # Special handling for TensorRT .engine models
            if current_model_path.endswith('.engine'):
                self.status_update.emit(f"Worker: Loading TensorRT .engine model ({current_model_path})...")
                self.yolo_model = YOLO(current_model_path, task='detect')  # TensorRT models typically use detect task
                self.status_update.emit(f"Worker: TensorRT model ({current_model_path}) loaded successfully.")
            # Check if this is a YOLOE model and use YOLOE class instead of YOLO
            elif self.is_yoloe_model:
                self.yolo_model = YOLOE(current_model_path, task=task)
                self.status_update.emit(f"Worker: YOLOE model ({current_model_path}) loaded with task: {task}.")
            else:
                self.yolo_model = YOLO(current_model_path, task=task)
                self.status_update.emit(f"Worker: YOLO model ({current_model_path}) loaded with task: {task}.")
            
            # Only move to CUDA if not already a TensorRT model (TensorRT models are pre-optimized)
            if not current_model_path.endswith('.engine'):
                self.yolo_model.to('cuda')
                self.status_update.emit(f"Worker: Model moved to CUDA.")
            else:
                self.status_update.emit(f"Worker: TensorRT model already optimized for GPU.")
            
            # Apply YOLOE text prompts ONLY if this is a YOLOE model (but not prompt-free version)
            if self.is_yoloe_model and '-pf' not in current_model_path.lower():
                self._apply_yoloe_classes(self.yolo_model, CLASS_NAMES)
            elif self.is_yoloe_model:
                self.status_update.emit("Worker: Using YOLOE prompt-free model - no text prompts needed.")
            else:
                # Standard YOLO models don't need explicit class setting
                self.status_update.emit("Worker: Standard YOLO model loaded - no class setting needed.")
            
            self.status_update.emit("Worker: Initializing ZED camera object...")
            self.zed = sl.Camera()
            self.init_params = sl.InitParameters(
                camera_resolution=ZED_RESOLUTION, camera_fps=ZED_FPS, depth_mode=ZED_DEPTH_MODE,
                coordinate_units=sl.UNIT.METER, coordinate_system=ZED_COORDINATE_SYSTEM,
                depth_minimum_distance=0.4, 
                depth_maximum_distance=20.0, enable_image_enhancement=True
            )
            self.status_update.emit("Worker: Opening ZED camera...")
            err = self.zed.open(self.init_params)
            if err != sl.ERROR_CODE.SUCCESS: raise Exception(f"ZED Open Error: {err}")
            else: 
                cam_info = self.zed.get_camera_information(); calib_params = cam_info.camera_configuration.calibration_parameters
                if calib_params is None or calib_params.left_cam is None: 
                    self.status_update.emit("[ZED WARNING] Calibration parameters structure missing!")
                elif calib_params.left_cam.fx == 0: 
                    self.status_update.emit("[ZED WARNING] Calibration fx=0 suggests failed self-calibration or missing factory calibration. Follow ZED's advice to point camera to a textured area >1m at startup.")
                else: 
                    self.status_update.emit("Worker: Camera calibration parameters seem valid (fx != 0).")
            self.status_update.emit("Worker: ZED camera opened.")
            
            self.status_update.emit("Worker: Enabling Positional Tracking...")
            pos_params = sl.PositionalTrackingParameters()
            pos_params.set_as_static = True 
            pos_params.enable_area_memory = True 
            err = self.zed.enable_positional_tracking(pos_params)
            if err != sl.ERROR_CODE.SUCCESS: raise Exception(f"Pos Tracking Error: {err}")
            self.status_update.emit("Worker: Positional Tracking enabled (static, area_memory).") 
            
            self.status_update.emit("Worker: Enabling Object Detection...")
            # Use configuration-based ZED settings for consistent behavior
            self.status_update.emit(f"Worker: ZED segmentation {'enabled' if ZED_ENABLE_SEGMENTATION else 'disabled'} via config")

            self.obj_det_params = sl.ObjectDetectionParameters(
                detection_model=sl.OBJECT_DETECTION_MODEL.CUSTOM_BOX_OBJECTS,
                enable_tracking=ZED_ENABLE_TRACKING,
                enable_segmentation=ZED_ENABLE_SEGMENTATION
            )
            err = self.zed.enable_object_detection(self.obj_det_params)
            if err != sl.ERROR_CODE.SUCCESS: raise Exception(f"ZED OD Error: {err}")
            self.status_update.emit("Worker: Object Detection enabled.")

            self.runtime_params = sl.RuntimeParameters(confidence_threshold=80, texture_confidence_threshold=80)
            self.obj_det_runtime_params = sl.ObjectDetectionRuntimeParameters(detection_confidence_threshold=ZED_DETECTION_CONFIDENCE_THRESHOLD)
            self.status_update.emit(f"Worker: ZED OD Runtime detection_confidence_threshold for custom objects set to {ZED_DETECTION_CONFIDENCE_THRESHOLD}.")
            
            self.status_update.emit("Worker: Initializing CSV files..."); initialize_csv(OBJECT_LOG_CSV, OBJECT_LOG_HEADER); initialize_csv(DISTANCE_LOG_CSV, DISTANCE_LOG_HEADER)
            self.status_update.emit("Worker: Initialization complete."); return True
        except Exception as e:
            self.status_update.emit(f"Worker: Initialization FAILED: {e}"); self.cleanup_resources(); return False

    def xyxy_to_zed_box_format(self, xyxy, img_width, img_height):
        """
        Convert YOLO xyxy format to ZED SDK CustomBoxObjectData format.
        Based on official ZED SDK YOLOv8 example implementation.

        Args:
            xyxy: [x1, y1, x2, y2] bounding box coordinates
            img_width: Image width
            img_height: Image height

        Returns:
            numpy array in ZED SDK format: [[x_min, y_min], [x_max, y_min], [x_max, y_max], [x_min, y_max]]
        """
        x1, y1, x2, y2 = xyxy

        # Normalize coordinates to [0, 1] range as expected by ZED SDK
        x_min = x1 / img_width
        x_max = x2 / img_width
        y_min = y1 / img_height
        y_max = y2 / img_height

        # ZED SDK expects 4 corner points in specific order:
        # A ------ B
        # | Object |
        # D ------ C
        output = np.zeros((4, 2), dtype=np.float32)
        output[0] = [x_min, y_min]  # A: top-left
        output[1] = [x_max, y_min]  # B: top-right
        output[2] = [x_max, y_max]  # C: bottom-right
        output[3] = [x_min, y_max]  # D: bottom-left

        return output

    def _determine_zed_segmentation_setting(self):
        """
        Determine whether to enable ZED SDK segmentation for better 3D position calculation.

        Returns:
            bool: Always True for better 3D position calculation with CustomMaskObjectData
        """
        self.status_update.emit("Worker: Enabling ZED segmentation for better 3D position calculation")
        return True

    def get_class_name_for_id(self, class_id):
        """
        Get the appropriate class name for a given class ID.
        Handles both standard YOLO models (0-79 COCO classes) and YOLOE models (open vocabulary).

        For YOLOE models, class_id should directly map to the classes we've set with set_classes().
        """
        if not self.is_yoloe_model:
            # Standard YOLO model - use COCO class names
            coco_names = [
                "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat",
                "traffic light", "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat",
                "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra", "giraffe", "backpack",
                "umbrella", "handbag", "tie", "suitcase", "frisbee", "skis", "snowboard", "sports ball",
                "kite", "baseball bat", "baseball glove", "skateboard", "surfboard", "tennis racket",
                "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple",
                "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake",
                "chair", "couch", "potted plant", "bed", "dining table", "toilet", "tv", "laptop",
                "mouse", "remote", "keyboard", "cell phone", "microwave", "oven", "toaster", "sink",
                "refrigerator", "book", "clock", "vase", "scissors", "teddy bear", "hair drier",
                "toothbrush"
            ]
            if 0 <= class_id < len(coco_names):
                return coco_names[class_id]
            else:
                return f"Unknown({class_id})"
        else:
            # YOLOE model - get class name directly from the model's names dictionary
            try:
                # Direct lookup in model.names dictionary (main approach for YOLOE)
                if hasattr(self.yolo_model, 'names') and isinstance(self.yolo_model.names, dict):
                    # Try with integer ID first
                    if class_id in self.yolo_model.names:
                        return self.yolo_model.names[class_id]
                    
                    # Some models might use string keys
                    str_id = str(class_id)
                    if str_id in self.yolo_model.names:
                        return self.yolo_model.names[str_id]
                
                # If not found in model.names, check if it's a valid index in CLASS_NAMES
                # (since we set the classes with CLASS_NAMES list)
                if 0 <= class_id < len(CLASS_NAMES):
                    return CLASS_NAMES[class_id]
                
                # If we still can't determine the class name, return a generic label
                return f"class_{class_id}"
                
            except Exception as e:
                self.status_update.emit(f"Worker Warning: Error getting YOLOE class name: {e}")
                return f"class_{class_id}"

    @Slot()
    def run_perception_loop(self):
        if not self.initialize_resources():
            self.status_update.emit("Worker thread stopping: Init failed."); self.finished.emit(); return
        with self._lock: self._running = True
        frame_count = 0
        while self._is_running():
            loop_start_time = time.time()  # Track processing time for FPS calculation
            
            # Check if model reload is requested
            self._check_and_reload_model()
            
            current_time = time.time()
            grab_status = self.zed.grab(self.runtime_params)
            if grab_status != sl.ERROR_CODE.SUCCESS:
                if grab_status == sl.ERROR_CODE.END_OF_SVOFILE_REACHED: self.stop()
                time.sleep(0.01); continue
            current_timestamp = self.zed.get_timestamp(sl.TIME_REFERENCE.IMAGE).get_seconds()
            self.zed.retrieve_image(self.left_image, sl.VIEW.LEFT); frame_bgr = self.left_image.get_data()
            if frame_bgr is None: continue
            
            clean_bgr_frame_for_llm = frame_bgr[:, :, :3].copy() 

            if frame_bgr.shape[2] == 4: frame_rgb = cv2.cvtColor(frame_bgr, cv2.COLOR_BGRA2RGB)
            else: frame_rgb = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)
            frame_display_for_emit = frame_bgr[:, :, :3].copy() 

            with self._threshold_lock: current_conf_thresh = self._current_conf_threshold
            with self._display_options_lock:
                show_yolo_plot_bboxes_labels_conf_flag = self._show_yolo_plot_bboxes_labels_conf
                show_yolo_plot_masks_flag = self._show_yolo_plot_masks
                show_id_flag = self._show_zed_id; show_pos_flag = self._show_zed_pos; show_speed_flag = self._show_zed_speed
            
            yolo_results_obj = None; custom_mask_detections_for_zed = []; self.current_frame_sl_masks.clear()
            try:
                yolo_results_list = self.yolo_model(frame_rgb, imgsz=640, conf=current_conf_thresh, device=0, verbose=False)
                if yolo_results_list and len(yolo_results_list) > 0:
                    yolo_results_obj = yolo_results_list[0]
                    if yolo_results_obj.boxes is not None:
                        # Handle both detection-only models (like TensorRT) and segmentation models
                        has_masks = yolo_results_obj.masks is not None
                        
                        for i in range(len(yolo_results_obj.boxes)):
                            box = yolo_results_obj.boxes[i]; xyxy = box.xyxy[0].cpu().numpy(); cls_id = int(box.cls[0]); conf = float(box.conf[0])

                            # ALWAYS use CustomMaskObjectData for better 3D position calculation
                            # CustomBoxObjectData often fails to provide 3D positions
                            zed_custom_det = sl.CustomMaskObjectData()
                            x1, y1, x2, y2 = map(int, xyxy)
                            zed_custom_det.bounding_box_2d = np.array([[x1, y1], [x2, y1], [x2, y2], [x1, y2]], dtype=np.float32)

                            zed_custom_det.label = cls_id; zed_custom_det.probability = conf; zed_custom_det.is_grounded = False
                            
                            # Handle masks for segmentation models, leave box_mask=None for detection-only models
                            if has_masks and hasattr(yolo_results_obj.masks, 'data') and yolo_results_obj.masks.data.shape[0] > i:
                                mask_data_full_frame = yolo_results_obj.masks.data[i].cpu().numpy()
                                m_h, m_w = mask_data_full_frame.shape
                                c_y1, c_y2 = max(0, y1), min(m_h, y2); c_x1, c_x2 = max(0, x1), min(m_w, x2)
                                if c_y1 < c_y2 and c_x1 < c_x2:
                                    mask_cropped_binary = (mask_data_full_frame[c_y1:c_y2, c_x1:c_x2] * 255).astype(np.uint8)
                                    if not mask_cropped_binary.flags.c_contiguous: mask_cropped_binary = np.ascontiguousarray(mask_cropped_binary)
                                    sl_mask_for_obj = sl.Mat(mask_cropped_binary.shape[1], mask_cropped_binary.shape[0], sl.MAT_TYPE.U8_C1, sl.MEM.CPU)
                                    numpy_view_of_sl_mask = sl_mask_for_obj.get_data()
                                    np.copyto(numpy_view_of_sl_mask, mask_cropped_binary)
                                    zed_custom_det.box_mask = sl_mask_for_obj; self.current_frame_sl_masks.append(sl_mask_for_obj)
                            # Note: For detection-only models, box_mask will be None but ZED can still calculate 3D positions from bounding_box_2d
                            custom_mask_detections_for_zed.append(zed_custom_det)
            except Exception as e:
                self.status_update.emit(f"YOLO Error or CustomMask Prep Error: {e}"); custom_mask_detections_for_zed = []

            # Ingest objects using CustomMaskObjectData for better 3D position calculation
            if custom_mask_detections_for_zed:
                self.zed.ingest_custom_mask_objects(custom_mask_detections_for_zed)
            
            self.zed.retrieve_objects(self.objects, self.obj_det_runtime_params)

            # Debug: Log how many objects ZED returned vs YOLO detected

            
            # === ENHANCED ANTI-GHOSTING: Get current active ZED object IDs with confidence tracking ===
            current_active_zed_ids = set()
            for obj in self.objects.object_list:
                if obj.tracking_state == sl.OBJECT_TRACKING_STATE.OK:
                    obj_id = obj.id
                    current_active_zed_ids.add(obj_id)
                    
                    # Update last seen time for active objects
                    self.object_last_seen[obj_id] = current_time
                    
                    # Track confidence history for ghosting prevention
                    if obj_id not in self.object_confidence_history:
                        self.object_confidence_history[obj_id] = []
                    
                    confidence = float(obj.confidence) if obj.confidence is not None else 0.0
                    self.object_confidence_history[obj_id].append(confidence)
                    
                    # Keep only recent confidence history
                    if len(self.object_confidence_history[obj_id]) > 10:
                        self.object_confidence_history[obj_id].pop(0)
            
            # === ENHANCED ANTI-GHOSTING: Clean up stale objects from latest_object_states ===
            with self.latest_states_lock:
                initial_count = len(self.latest_object_states)
                
                # Remove objects that are no longer being tracked by ZED
                self.latest_object_states = [
                    obj_data for obj_data in self.latest_object_states 
                    if obj_data.get('object_id') in current_active_zed_ids
                ]
                
                # Also remove objects that haven't been seen for too long OR have low confidence
                objects_to_remove = []
                for obj_data in self.latest_object_states:
                    obj_id = obj_data.get('object_id')
                    last_seen = self.object_last_seen.get(obj_id, 0)
                    
                    # Check if object has timed out
                    has_timed_out = current_time - last_seen > self.object_timeout
                    
                    # Check if object has consistently low confidence (possible ghost)
                    has_low_confidence = False
                    if obj_id in self.object_confidence_history and len(self.object_confidence_history[obj_id]) >= 3:
                        recent_confidences = self.object_confidence_history[obj_id][-5:]  # Last 5 frames
                        avg_confidence = np.mean(recent_confidences)
                        has_low_confidence = avg_confidence < 0.3  # 30% confidence threshold
                    
                    if has_timed_out or has_low_confidence:
                        objects_to_remove.append(obj_data)
                        if obj_id in self.object_last_seen:
                            del self.object_last_seen[obj_id]
                        if obj_id in self.object_confidence_history:
                            del self.object_confidence_history[obj_id]
                
                # Remove timeout/low-confidence objects
                for obj_to_remove in objects_to_remove:
                    if obj_to_remove in self.latest_object_states:
                        self.latest_object_states.remove(obj_to_remove)
                
                final_count = len(self.latest_object_states)
                if initial_count > final_count:
                    removed_count = initial_count - final_count
                    # Silently clean up stale objects without status message to reduce log noise
                    # self.status_update.emit(f"Worker: Cleaned up {removed_count} stale object(s)")
            
            # === CLEAN UP HISTORIES FOR REMOVED OBJECTS ===
            # Remove speed history and confidence history for objects that are no longer active
            speed_history_ids_to_remove = []
            confidence_history_ids_to_remove = []
            
            for obj_id in self.speed_history:
                if obj_id not in current_active_zed_ids:
                    speed_history_ids_to_remove.append(obj_id)
            
            for obj_id in self.object_confidence_history:
                if obj_id not in current_active_zed_ids:
                    confidence_history_ids_to_remove.append(obj_id)
            
            for obj_id in speed_history_ids_to_remove:
                del self.speed_history[obj_id]
                
            for obj_id in confidence_history_ids_to_remove:
                del self.object_confidence_history[obj_id]
            
            object_rows_to_write = []; distance_rows_to_write = []; temp_objects_this_frame = [] 
            _temp_class_names_by_id = {} 
            current_frame_distances = [] 

            if self.objects.is_new:
                img_h = self.left_image.get_height() 
                img_w = self.left_image.get_width()
                
                current_frame_ok_and_visible_objects = []
                for obj in self.objects.object_list:
                    # Allow objects in OK or SEARCHING state to be processed
                    # Objects without positions may get them assigned during processing
                    if (obj.tracking_state == sl.OBJECT_TRACKING_STATE.OK or 
                        obj.tracking_state == sl.OBJECT_TRACKING_STATE.SEARCHING):
                        
                        if obj.bounding_box_2d is not None and len(obj.bounding_box_2d) == 4:
                            all_x = np.array([p[0] for p in obj.bounding_box_2d])
                            all_y = np.array([p[1] for p in obj.bounding_box_2d])
                            xmin, xmax = np.min(all_x), np.max(all_x)
                            ymin, ymax = np.min(all_y), np.max(all_y)

                            box_center_x = (xmin + xmax) / 2
                            box_center_y = (ymin + ymax) / 2
                            
                            has_valid_area = (xmax - xmin) > 0 and (ymax - ymin) > 0
                            is_center_in_view = (box_center_x >= 0 and box_center_x < img_w and
                                                 box_center_y >= 0 and box_center_y < img_h)
                            
                            # Additional validation: check if position is reasonable (not too far) - only if position exists
                            is_reasonable_distance = True  # Default to true
                            if obj.position is not None and not np.isnan(obj.position).any():
                                position_distance = np.linalg.norm(obj.position)
                                is_reasonable_distance = position_distance < 50.0  # Max 50 meters

                            if is_center_in_view and has_valid_area and is_reasonable_distance:
                                current_frame_ok_and_visible_objects.append(obj)
                        else:
                            # Fallback: include objects without bounding boxes if they have valid positions
                            if obj.position is not None and not np.isnan(obj.position).any():
                                position_distance = np.linalg.norm(obj.position)
                                if position_distance < 50.0:  # Max 50 meters
                                    current_frame_ok_and_visible_objects.append(obj)
                
                valid_tracked_objects = current_frame_ok_and_visible_objects
                
                for zed_obj in valid_tracked_objects:
                    object_id = zed_obj.id; zed_box_raw = zed_obj.bounding_box_2d; zed_box_xyxy = zed_box_to_xyxy(zed_box_raw)
                    # Note: zed_box_xyxy might be None for objects without bounding boxes, but they can still have positions

                    label_id_to_log_int = -1
                    if zed_obj.raw_label is not None:
                        try:
                            label_id_to_log_int = int(zed_obj.raw_label)
                        except ValueError:
                            label_id_to_log_int = -1


                                     
                    current_mask_status_for_log = 0
                    if zed_obj.mask and zed_obj.mask.is_init():
                        if zed_obj.mask.get_width() > 0 and zed_obj.mask.get_height() > 0: current_mask_status_for_log = 1
                    
                    position = zed_obj.position; velocity = zed_obj.velocity; zed_confidence = zed_obj.confidence
                    class_name_to_log = self.get_class_name_for_id(label_id_to_log_int)
                    pos_is_valid = position is not None and not np.isnan(position).any(); vel_is_valid = velocity is not None and not np.isnan(velocity).any()

                    # Position smoothing removed - all objects treated equally without special processing

                    # Calculate 3D speed in m/s (magnitude of velocity vector)
                    current_speed = 0.0
                    current_speed_2d = 0.0  # Horizontal speed (X-Y plane)
                    if vel_is_valid:
                        # 3D speed (full magnitude including vertical movement)
                        current_speed = np.linalg.norm(np.array(velocity))
                        # 2D horizontal speed (excluding Z-axis for ground movement analysis)
                        current_speed_2d = np.linalg.norm(np.array([velocity[0], velocity[1]]))
                    
                    # === SPEED SMOOTHING AND STATIC OBJECT DETECTION ===
                    # Initialize speed history for new objects
                    if object_id not in self.speed_history:
                        self.speed_history[object_id] = []
                    
                    # Add current speed to history
                    self.speed_history[object_id].append(current_speed)
                    
                    # Keep only recent history
                    if len(self.speed_history[object_id]) > self.speed_history_length:
                        self.speed_history[object_id].pop(0)
                    
                    # Calculate smoothed speed (average over recent frames)
                    smoothed_speed = np.mean(self.speed_history[object_id])
                    smoothed_speed_2d = current_speed_2d  # For now, only smooth 3D speed
                    
                    # Determine if object is static using UNIVERSAL threshold for ALL objects
                    # No special treatment for any object type - all objects treated equally
                    is_static = smoothed_speed < self.speed_threshold_static

                    # For static objects, set displayed speed to 0
                    display_speed = 0.0 if is_static else smoothed_speed
                    display_speed_2d = 0.0 if is_static else current_speed_2d

                    # Debug logging removed - was too noisy for normal operation
                    
                    # Convert to km/h for more intuitive understanding
                    current_speed_kmh = current_speed * 3.6  # m/s to km/h (raw)
                    current_speed_2d_kmh = current_speed_2d * 3.6  # m/s to km/h (raw)
                    display_speed_kmh = display_speed * 3.6  # Filtered speed
                    display_speed_2d_kmh = display_speed_2d * 3.6  # Filtered speed
                    
                    data_entry = {
                        "timestamp": current_timestamp, "frame_id": frame_count, "object_id": object_id,
                        "label": label_id_to_log_int, "class_name": class_name_to_log,
                        "confidence": float(zed_confidence) if zed_confidence is not None else 0.0,
                        "position": position.tolist() if pos_is_valid else None, 
                        "velocity": velocity.tolist() if vel_is_valid else None,
                        "speed": current_speed, "speed_kmh": current_speed_kmh,
                        "speed_2d": current_speed_2d, "speed_2d_kmh": current_speed_2d_kmh,
                        "speed_filtered": display_speed, "speed_filtered_kmh": display_speed_kmh,
                        "speed_2d_filtered": display_speed_2d, "speed_2d_filtered_kmh": display_speed_2d_kmh,
                        "is_static": is_static, "smoothed_speed": smoothed_speed,
                        "bounding_box_2d": zed_box_xyxy, 
                        "mask_available": current_mask_status_for_log
                    }
                    # Allow objects to be processed to get positions assigned, but filter out extremely low confidence
                    obj_confidence = float(zed_confidence) if zed_confidence is not None else 0.0
                    
                    # Relaxed validation: allow objects without valid positions (they may get positions during tracking)
                    # but still filter out objects with extremely low confidence to reduce noise
                    if obj_confidence > 0.1:  # Very low threshold - just filter out complete noise
                        temp_objects_this_frame.append(data_entry) 
                        _temp_class_names_by_id[object_id] = class_name_to_log 

                        object_rows_to_write.append([
                            f"{current_timestamp:.4f}", frame_count, object_id, label_id_to_log_int, class_name_to_log, f"{data_entry['confidence']:.2f}",
                            f"{position[0]:.3f}" if pos_is_valid else "", f"{position[1]:.3f}" if pos_is_valid else "", f"{position[2]:.3f}" if pos_is_valid else "",
                            f"{velocity[0]:.3f}" if vel_is_valid else "", f"{velocity[1]:.3f}" if vel_is_valid else "", f"{velocity[2]:.3f}" if vel_is_valid else "",
                            f"{current_speed:.3f}", f"{current_speed_kmh:.1f}", f"{current_speed_2d:.3f}", f"{current_speed_2d_kmh:.1f}",
                            f"{display_speed:.3f}", f"{display_speed_kmh:.1f}", f"{display_speed_2d:.3f}", f"{display_speed_2d_kmh:.1f}",
                            str(is_static), f"{smoothed_speed:.3f}", str(zed_box_xyxy), current_mask_status_for_log
                        ])
                
                # Distance calculations...
                if len(valid_tracked_objects) >= 2:
                    for i in range(len(valid_tracked_objects)):
                        for j in range(i + 1, len(valid_tracked_objects)):
                            obj_A=valid_tracked_objects[i]; obj_B=valid_tracked_objects[j]; 
                            pos_A=obj_A.position; pos_B=obj_B.position
                            if (pos_A is not None and not np.isnan(pos_A).any() and 
                                pos_B is not None and not np.isnan(pos_B).any()):
                                dist = np.linalg.norm(pos_A - pos_B)
                                distance_rows_to_write.append([f"{current_timestamp:.4f}", frame_count, obj_A.id, obj_B.id, f"{dist:.4f}"])
                                
                                class_A = _temp_class_names_by_id.get(obj_A.id, 'N/A')
                                class_B = _temp_class_names_by_id.get(obj_B.id, 'N/A')
                                current_frame_distances.append({
                                    "id1": obj_A.id, "class1": class_A,
                                    "id2": obj_B.id, "class2": class_B,
                                    "distance": dist
                                })

            # === FIXED: Replace latest_object_states with ONLY current frame data ===
            with self.latest_states_lock:
                self.latest_object_states = temp_objects_this_frame.copy()
            
            try:
                if object_rows_to_write:
                     with open(OBJECT_LOG_CSV, 'a', newline='') as f: csv.writer(f).writerows(object_rows_to_write)
                if distance_rows_to_write:
                     with open(DISTANCE_LOG_CSV, 'a', newline='') as f: csv.writer(f).writerows(distance_rows_to_write)
            except IOError as e: self.status_update.emit(f"CSV Write Error: {e}")
            
            annotated_frame = frame_display_for_emit.copy() 
            if yolo_results_obj is not None and (show_yolo_plot_bboxes_labels_conf_flag or show_yolo_plot_masks_flag):
                 try:
                      annotated_frame = yolo_results_obj.plot(
                          img=annotated_frame, conf=show_yolo_plot_bboxes_labels_conf_flag, labels=show_yolo_plot_bboxes_labels_conf_flag, 
                          boxes=show_yolo_plot_bboxes_labels_conf_flag, masks=show_yolo_plot_masks_flag
                      )
                 except Exception as e_plot: self.status_update.emit(f"Worker Warning: Error plotting YOLO results: {e_plot}")
            
            if self.objects.is_new and (show_id_flag or show_pos_flag or show_speed_flag):
                 font_face=cv2.FONT_HERSHEY_SIMPLEX; font_scale=0.5; thickness=1; text_padding=3; line_spacing=3
                 temp_objects_dict_for_overlay = {obj_data['object_id']: obj_data for obj_data in temp_objects_this_frame}
                 for zed_obj_for_overlay in self.objects.object_list:
                      track_id = zed_obj_for_overlay.id; state = zed_obj_for_overlay.tracking_state; bb_2d_zed = zed_obj_for_overlay.bounding_box_2d
                      current_frame_obj_data_for_overlay = temp_objects_dict_for_overlay.get(track_id) 
                      
                      if state == sl.OBJECT_TRACKING_STATE.OK:
                          if not current_frame_obj_data_for_overlay: continue 
                          text_color=(255, 255, 255); bg_color=(0, 128, 0)
                      elif state == sl.OBJECT_TRACKING_STATE.SEARCHING:
                          text_color=(0, 0, 0); bg_color=(0, 200, 255)
                          if not current_frame_obj_data_for_overlay: 
                              class_id_searching = int(zed_obj_for_overlay.raw_label) if zed_obj_for_overlay.raw_label is not None else -1
                              current_frame_obj_data_for_overlay = {"class_name": self.get_class_name_for_id(class_id_searching)}
                      else: continue 
                      
                      obj_pos_list = current_frame_obj_data_for_overlay.get('position')
                      obj_speed_filtered = current_frame_obj_data_for_overlay.get('speed_filtered', 0.0)
                      obj_is_static = current_frame_obj_data_for_overlay.get('is_static', False)
                      obj_class_name = current_frame_obj_data_for_overlay.get('class_name', 'N/A')

                      if bb_2d_zed is not None and len(bb_2d_zed) == 4:
                           try:
                               tr_corner_x, tr_corner_y = map(int, bb_2d_zed[1]) 
                               text_lines_to_draw = []
                               if show_id_flag: text_lines_to_draw.append(f"ID:{track_id} ({obj_class_name})")
                               if show_pos_flag:
                                   pos_text = "P:N/A"
                                   if obj_pos_list and len(obj_pos_list) == 3: pos_text = f"P:{obj_pos_list[0]:.1f},{obj_pos_list[1]:.1f},{obj_pos_list[2]:.1f}m"
                                   text_lines_to_draw.append(pos_text)
                               if show_speed_flag:
                                   if obj_speed_filtered is not None:
                                       obj_speed_filtered_kmh = obj_speed_filtered * 3.6  # Convert m/s to km/h
                                       if obj_is_static:
                                           speed_text = "S: STATIC"
                                       elif obj_speed_filtered_kmh >= 1.0:  # Show km/h for speeds >= 1 km/h
                                           speed_text = f"S: {obj_speed_filtered_kmh:.1f}km/h"
                                       else:  # Show m/s for very low speeds
                                           speed_text = f"S: {obj_speed_filtered:.2f}m/s"
                                   else:
                                       speed_text = "S: N/A"
                                   text_lines_to_draw.append(speed_text)
                               if not text_lines_to_draw: continue
                               text_sizes_with_bl = [cv2.getTextSize(line, font_face, font_scale, thickness) for line in text_lines_to_draw]
                               text_heights = [size[0][1] for size in text_sizes_with_bl]; max_text_width = 0
                               if text_sizes_with_bl : max_text_width = max(size[0][0] for size in text_sizes_with_bl)
                               total_text_block_height = sum(text_heights) + (len(text_lines_to_draw) -1) * line_spacing if text_lines_to_draw else 0
                               rect_x2 = tr_corner_x - 5; rect_x1 = rect_x2 - max_text_width - 2 * text_padding 
                               rect_y1 = tr_corner_y + 5; rect_y2 = rect_y1 + total_text_block_height + 2 * text_padding
                               h_img, w_img = annotated_frame.shape[:2];
                               draw_x1 = max(0, rect_x1); draw_y1 = max(0, rect_y1); draw_x2 = min(w_img - 1, rect_x2); draw_y2 = min(h_img - 1, rect_y2)
                               draw_x1 = max(0, draw_x2 - max_text_width - 2 * text_padding)
                               if draw_x1 < draw_x2 and draw_y1 < draw_y2: 
                                    cv2.rectangle(annotated_frame, (draw_x1, draw_y1), (draw_x2, draw_y2), bg_color, cv2.FILLED)
                                    current_y_text_start = draw_y1 + text_padding 
                                    for i_line, line in enumerate(text_lines_to_draw): 
                                         text_y_coord = current_y_text_start + text_heights[i_line] 
                                         cv2.putText(annotated_frame, line, (draw_x1 + text_padding, text_y_coord), font_face, font_scale, text_color, thickness, cv2.LINE_AA)
                                         current_y_text_start = text_y_coord + line_spacing 
                           except Exception as e_draw: self.status_update.emit(f"Worker Warning: Error drawing ZED overlay for ID {track_id}: {e_draw}")
            
            self.frame_ready.emit(annotated_frame)
            
            with self.latest_states_lock: 
                data_to_emit = [copy.deepcopy(obj_data) for obj_data in self.latest_object_states] 
            
            self.scene_data_ready.emit(data_to_emit, clean_bgr_frame_for_llm, current_frame_distances) 
            
            # Calculate and emit statistics
            loop_end_time = time.time()
            loop_duration = loop_end_time - loop_start_time
            self.stats_frame_times.append(loop_duration)
            
            # Keep only recent frame times for rolling average
            if len(self.stats_frame_times) > self.stats_max_frame_history:
                self.stats_frame_times.pop(0)
            
            # Calculate FPS (inverse of average frame processing time)
            avg_frame_time = np.mean(self.stats_frame_times) if self.stats_frame_times else 0.1
            fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0.0
            
            # Count objects and active objects
            total_objects = len(temp_objects_this_frame)
            active_objects = len([obj for obj in temp_objects_this_frame if not obj.get('is_static', False)])
            
            # Get system resource usage
            system_stats = self._get_system_stats()
            
            # Emit statistics every 5 frames to avoid overwhelming the GUI
            if frame_count % 5 == 0:
                current_timestamp = time.time()
                stats_data = {
                    'timestamp': current_timestamp,
                    'objects': total_objects,
                    'fps': fps,
                    'active': active_objects,
                    'frame_count': frame_count,
                    'cpu_percent': system_stats['cpu_percent'],
                    'gpu_percent': system_stats['gpu_percent'],
                    'memory_percent': system_stats['memory_percent'],
                    'gpu_memory_percent': system_stats['gpu_memory_percent']
                }
                self.stats_update.emit(stats_data)
                
                # Log performance statistics to CSV if enabled
                with self._display_options_lock:
                    log_performance = self._log_performance
                
                if log_performance:
                    try:
                        with open(PERFORMANCE_LOG_CSV, 'a', newline='') as f:
                            csv.writer(f).writerow([
                                f"{current_timestamp:.4f}",
                                frame_count,
                                f"{fps:.1f}",
                                total_objects,
                                active_objects,
                                f"{system_stats['cpu_percent']:.1f}",
                                f"{system_stats['gpu_percent']:.1f}",
                                f"{system_stats['memory_percent']:.1f}",
                                f"{system_stats['gpu_memory_percent']:.1f}"
                            ])
                    except Exception as e:
                        self.status_update.emit(f"Worker: Failed to log performance stats: {e}")
            
            frame_count += 1; QApplication.processEvents() 
        self.cleanup_resources(); self.finished.emit()

    @Slot()
    def stop(self):
         with self._lock: self._running = False

    def cleanup_resources(self):
        self.status_update.emit("Worker: Cleaning up resources...")
        try:
            if hasattr(self, 'zed') and self.zed is not None and self.zed.is_opened():
                self.zed.close()
                self.status_update.emit("Worker: ZED camera closed.")
            self.zed = None
        except Exception as e_close:
            self.status_update.emit(f"Worker: Error closing ZED camera: {e_close}")
        finally:
            self.zed = None
        self.status_update.emit("Worker: Resources cleaned up.")

# --- Whisper Worker (moved to ai/whisper_worker.py) ---

# --- OpenRouter API Worker (moved to ai/openrouter_worker.py) ---


# --- OpenRouter Worker (moved to ai/openrouter_worker.py) ---



# --- Main Window ---
class MainWindow(QMainWindow):
    confidence_threshold_changed = Signal(float)
    stop_perception_signal = Signal()
    show_yolo_plot_bboxes_labels_conf_changed = Signal(bool)
    show_yolo_plot_masks_changed = Signal(bool)
    show_zed_id_changed = Signal(bool)
    show_zed_pos_changed = Signal(bool)
    show_zed_speed_changed = Signal(bool)
    model_changed = Signal(str)  # Signal to notify worker of model change
    
    # OpenRouter signals
    online_model_changed = Signal(str)
    openrouter_scene_request = Signal(object, object, object)  # (clean_image, annotated_image, object_data_list)
    
    # Ollama signals
    ollama_model_changed = Signal(str)
    ollama_scene_request = Signal(object, object, object)  # (clean_image, annotated_image, object_data_list)
    
    # Common signals for both providers
    scene_request = Signal(object, object)  # (image, object_data_list)
    annotated_scene_request = Signal(object, object, object)  # (clean_image, annotated_image, object_data_list)
    
    # Statistics signal
    stats_update = Signal(dict)  # Signal for statistics updates

    @Slot(dict)
    def update_statistics_display(self, stats):
        """Update the statistics display with current metrics"""
        objects = stats.get('objects', 0)
        fps = stats.get('fps', 0.0)
        active = stats.get('active', 0)
        frame_count = stats.get('frame_count', 0)
        cpu_percent = stats.get('cpu_percent', 0.0)
        gpu_percent = stats.get('gpu_percent', 0.0)
        memory_percent = stats.get('memory_percent', 0.0)
        gpu_memory_percent = stats.get('gpu_memory_percent', 0.0)
        
        # Create multi-line statistics display
        line1 = f"Objects: {objects} | FPS: {fps:.1f} | Active: {active} | Frames: {frame_count}"
        line2 = f"CPU: {cpu_percent:.1f}% | GPU: {gpu_percent:.1f}% | RAM: {memory_percent:.1f}% | VRAM: {gpu_memory_percent:.1f}%"
        stats_text = f"{line1}\n{line2}"
        
        # Add a note if we're on Jetson and GPU stats are 0
        if gpu_percent == 0.0 and gpu_memory_percent == 0.0:
            platform = self.worker._detect_jetson_platform() if hasattr(self, 'worker') and hasattr(self.worker, '_detect_jetson_platform') else False
            if platform:
                stats_text += "\n(Note: GPU stats logging to CSV even if not displayed)"
        
        self.stats_display.setText(stats_text)

    def __init__(self):
        super().__init__()
        self.current_model = DEFAULT_YOLO_MODEL
        # self.setWindowTitle(f"pAIrSEEption - {self.current_model}")
        self.setWindowTitle(f"pAIrSEEption")
        self.setGeometry(100, 100, 1800, 900)  # Increased width for 3-panel layout
        
        # Initialize main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)
        
        # Left Panel for Video and Controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Video Display
        self.video_label = QLabel("Video will appear here")
        self.video_label.setMinimumSize(800, 450)
        self.video_label.setStyleSheet("""
            border: 1px solid black; 
            background-color: #2b2b2b; 
            color: white;
            font-size: 14px;
            font-weight: bold;
        """)
        self.video_label.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(self.video_label)
        
        # Control Panel
        control_group = QGroupBox("Detection Controls")
        control_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        control_layout = QGridLayout(control_group)
        
        # Start/Stop Button
        self.start_button = QPushButton("Start pAIrSEEption")
        self.start_button.clicked.connect(self.toggle_perception)
        self.start_button.setMinimumHeight(50)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        control_layout.addWidget(self.start_button, 0, 0)
        
        # Model Selection
        control_layout.addWidget(QLabel("YOLO Model:"), 1, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems(list(AVAILABLE_YOLO_MODELS.keys()))
        self.model_combo.setCurrentText(DEFAULT_YOLO_MODEL)
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        control_layout.addWidget(self.model_combo, 1, 1)
        
        # Confidence Threshold Slider
        control_layout.addWidget(QLabel("Confidence Threshold:"), 2, 0)
        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setMinimum(1)
        self.confidence_slider.setMaximum(100)
        self.confidence_slider.setValue(int(INITIAL_CONFIDENCE_THRESHOLD * 100))
        self.confidence_slider.valueChanged.connect(self.confidence_slider_changed)
        control_layout.addWidget(self.confidence_slider, 2, 1)
        
        self.conf_value_label = QLabel(f"{INITIAL_CONFIDENCE_THRESHOLD:.2f}")
        control_layout.addWidget(self.conf_value_label, 2, 2)
        
        # Display Options
        self.cb_show_yolo_plot_bbox_label_conf = QCheckBox("Show YOLO Bboxes/Labels/Conf")
        self.cb_show_yolo_plot_bbox_label_conf.setChecked(True)
        self.cb_show_yolo_plot_bbox_label_conf.stateChanged.connect(self.on_display_option_changed)
        self.cb_show_yolo_plot_bbox_label_conf.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 12px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #45a049;
                border: 2px solid #45a049;
            }
        """)
        control_layout.addWidget(self.cb_show_yolo_plot_bbox_label_conf, 3, 0)
        
        self.cb_show_yolo_masks = QCheckBox("Show YOLO Masks")
        self.cb_show_yolo_masks.setChecked(True)
        self.cb_show_yolo_masks.stateChanged.connect(self.on_display_option_changed)
        self.cb_show_yolo_masks.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 12px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #45a049;
                border: 2px solid #45a049;
            }
        """)
        control_layout.addWidget(self.cb_show_yolo_masks, 3, 1)
        
        self.cb_show_zed_id = QCheckBox("Show ZED ID")
        self.cb_show_zed_id.setChecked(True)
        self.cb_show_zed_id.stateChanged.connect(self.on_display_option_changed)
        self.cb_show_zed_id.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 12px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #45a049;
                border: 2px solid #45a049;
            }
        """)
        control_layout.addWidget(self.cb_show_zed_id, 4, 0)
        
        # Create a new row for ZED Position
        self.cb_show_zed_pos = QCheckBox("Show ZED Position")
        self.cb_show_zed_pos.setChecked(True)
        self.cb_show_zed_pos.stateChanged.connect(self.on_display_option_changed)
        self.cb_show_zed_pos.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 12px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #45a049;
                border: 2px solid #45a049;
            }
        """)
        control_layout.addWidget(self.cb_show_zed_pos, 5, 0)  # Moved to row 5
        
        self.cb_show_zed_speed = QCheckBox("Show ZED Speed")
        self.cb_show_zed_speed.setChecked(True)
        self.cb_show_zed_speed.stateChanged.connect(self.on_display_option_changed)
        self.cb_show_zed_speed.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 12px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #45a049;
                border: 2px solid #45a049;
            }
        """)
        control_layout.addWidget(self.cb_show_zed_speed, 5, 1)  # Moved to row 5
        
        # Text prompt input for YOLOe models
        self.yoloe_prompt_group = QGroupBox("YOLOe Text Prompt")
        self.yoloe_prompt_group.setVisible(False)  # Hidden by default
        self.yoloe_prompt_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: white;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        yoloe_prompt_layout = QHBoxLayout(self.yoloe_prompt_group)
        
        self.class_names_input = QLineEdit()
        self.class_names_input.setPlaceholderText("Enter class names separated by commas (e.g., person, chair, tv)")
        self.class_names_input.setText(", ".join(CLASS_NAMES))  # Set current class names
        self.class_names_input.setStyleSheet("""
            QLineEdit {
                background-color: #2b2b2b;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)
        
        self.apply_class_names_button = QPushButton("Apply")
        self.apply_class_names_button.clicked.connect(self.on_apply_class_names)
        self.apply_class_names_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        yoloe_prompt_layout.addWidget(self.class_names_input)
        yoloe_prompt_layout.addWidget(self.apply_class_names_button)
        
        control_layout.addWidget(self.yoloe_prompt_group, 6, 0, 1, 3)  # Moved to row 6, span all columns
        
        # Statistics Section
        stats_header_layout = QHBoxLayout()
        stats_header_layout.addWidget(QLabel("📊 Detection Statistics:"))
        stats_header_layout.addStretch()
        control_layout.addLayout(stats_header_layout, 7, 0, 1, 3)  # Moved to row 7
        
        # Add Log Performance Stats checkbox in its own row
        log_perf_layout = QHBoxLayout()
        self.cb_log_performance = QCheckBox("Log Performance Stats")
        self.cb_log_performance.setChecked(False)  # Default: no logging
        self.cb_log_performance.stateChanged.connect(self.on_log_performance_changed)
        self.cb_log_performance.setToolTip("Log performance statistics to CSV file")
        self.cb_log_performance.setStyleSheet("""
            QCheckBox {
                color: #FF9800;
                font-size: 11px;
                font-weight: bold;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 14px;
                height: 14px;
                border: 2px solid #555;
                border-radius: 3px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #FF9800;
                border: 2px solid #FF9800;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)
        log_perf_layout.addWidget(self.cb_log_performance)
        log_perf_layout.addStretch()
        control_layout.addLayout(log_perf_layout, 8, 0, 1, 3)  # Add in row 8
        
        self.stats_display = QLabel("Objects: 0 | FPS: 0.0 | Active: 0\nCPU: 0.0% | GPU: 0.0% | RAM: 0.0% | VRAM: 0.0%")
        self.stats_display.setStyleSheet("""
            QLabel {
                background-color: #333;
                color: #00ff00;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 4px;
                font-family: 'Courier New', monospace;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        self.stats_display.setAlignment(Qt.AlignLeft | Qt.AlignTop)  # Enable multi-line alignment
        self.stats_display.setMaximumHeight(50)  # Limit the height to make it more compact
        control_layout.addWidget(self.stats_display, 9, 0, 1, 3)  # Moved to row 9
        
        left_layout.addWidget(control_group)
        
        # Middle Panel for AI Interaction
        middle_panel = QWidget()
        middle_layout = QVBoxLayout(middle_panel)
        
        # Whisper Controls
        whisper_group = QGroupBox("Voice Transcription")
        whisper_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        whisper_layout = QVBoxLayout(whisper_group)
        
        # Whisper Model Selection
        whisper_model_layout = QHBoxLayout()
        whisper_model_layout.addWidget(QLabel("🧠 Whisper Model:"))
        whisper_model_layout.addStretch()
        whisper_layout.addLayout(whisper_model_layout)
        
        self.whisper_model_combo = QComboBox()
        self.whisper_model_combo.addItems(["tiny", "base", "small", "medium", "large"])
        self.whisper_model_combo.setCurrentText("base")
        self.whisper_model_combo.currentTextChanged.connect(self.on_whisper_model_changed)
        self.whisper_model_combo.setMinimumHeight(30)
        self.whisper_model_combo.setToolTip("Note: 'large' uses larg e-v3-turbo for better efficiency, 'medium' models may use significant GPU memory.\nWill try GPU first, then fallback to CPU if needed.")
        whisper_layout.addWidget(self.whisper_model_combo)
        
        # Whisper Optimization Controls
        whisper_opt_layout = QHBoxLayout()
        
        # Language Selection
        whisper_opt_layout.addWidget(QLabel("🌍 Language:"))
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "de (German)", "en (English)", "fr (French)", "es (Spanish)", 
            "it (Italian)", "auto (Auto-detect)"
        ])
        self.language_combo.setCurrentText("de (German)")
        self.language_combo.currentTextChanged.connect(self.on_language_changed)
        self.language_combo.setMinimumHeight(25)
        self.language_combo.setToolTip("Language hint for better accuracy. German is optimized for your use case.")
        whisper_opt_layout.addWidget(self.language_combo)
        
        # Duration dropdown removed - Push-to-Talk controls duration manually
        
        whisper_layout.addLayout(whisper_opt_layout)
        
        # Audio Debugging Controls
        debug_layout = QHBoxLayout()
        self.save_audio_checkbox = QCheckBox("💾 Save Audio Files")
        self.save_audio_checkbox.setChecked(False)
        self.save_audio_checkbox.stateChanged.connect(self.on_save_audio_changed)
        self.save_audio_checkbox.setToolTip("Save recorded audio files for debugging/analysis")
        self.save_audio_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 13px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #2196F3;
                border: 2px solid #2196F3;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #1976D2;
                border: 2px solid #1976D2;
            }
        """)
        debug_layout.addWidget(self.save_audio_checkbox)
        
        debug_layout.addStretch()
        whisper_layout.addLayout(debug_layout)

        # Voice Input Mode Selection
        input_mode_layout = QHBoxLayout()
        input_mode_label = QLabel("Voice Input Mode:")
        input_mode_label.setStyleSheet("color: white; font-weight: bold;")
        input_mode_layout.addWidget(input_mode_label)

        self.voice_input_mode = QComboBox()
        self.voice_input_mode.addItems(["Push-to-Talk"])
        self.voice_input_mode.setCurrentText("Push-to-Talk")  # Default
        self.voice_input_mode.currentTextChanged.connect(self.on_voice_input_mode_changed)
        self.voice_input_mode.setMinimumHeight(30)  # Same as other dropdowns
        input_mode_layout.addWidget(self.voice_input_mode)
        input_mode_layout.addStretch()
        whisper_layout.addLayout(input_mode_layout)

        # Voice Input Status
        self.voice_status_label = QLabel("Mode: Push-to-Talk (Button or Spacebar)")
        self.voice_status_label.setStyleSheet("color: #888; font-style: italic; margin: 5px 0;")
        whisper_layout.addWidget(self.voice_status_label)

        # Voice Input Buttons Layout
        voice_buttons_layout = QHBoxLayout()

        # Push-to-Talk Button (only visible in Push-to-Talk mode)
        self.push_to_talk_button = QPushButton("🎤 Hold to Talk")
        self.push_to_talk_button.setCheckable(True)
        self.push_to_talk_button.pressed.connect(self.start_push_to_talk_recording)
        self.push_to_talk_button.released.connect(self.stop_push_to_talk_recording)
        self.push_to_talk_button.setMinimumHeight(40)
        self.push_to_talk_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:pressed {
                background-color: #f44336;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        voice_buttons_layout.addWidget(self.push_to_talk_button)

        # Original Listen Button (for countdown mode and VAD)
        self.listen_button = QPushButton("🎤 Listen and Transcribe")
        self.listen_button.setCheckable(True)
        self.listen_button.clicked.connect(self.on_listen_button_clicked)
        self.listen_button.setVisible(False)  # Hidden by default
        voice_buttons_layout.addWidget(self.listen_button)

        whisper_layout.addLayout(voice_buttons_layout)
        
        self.transcription_display = QTextEdit()
        self.transcription_display.setMaximumHeight(80)
        self.transcription_display.setPlaceholderText("Transcriptions will appear here...")
        self.transcription_display.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: #2b2b2b;
                color: white;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        whisper_layout.addWidget(self.transcription_display)
        
        middle_layout.addWidget(whisper_group)
        
        # AI Vision Analysis
        ai_group = QGroupBox("AI Vision Analysis")
        ai_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        ai_layout = QVBoxLayout(ai_group)
        
        # Provider selection dropdown
        provider_header = QHBoxLayout()
        provider_header.addWidget(QLabel("🌐 AI Provider:"))
        provider_header.addStretch()
        ai_layout.addLayout(provider_header)
        
        self.provider_combo = QComboBox()
        self.provider_combo.addItems(["OpenRouter", "Ollama"])
        self.provider_combo.setCurrentText("OpenRouter")
        self.provider_combo.currentTextChanged.connect(self.on_provider_changed)
        self.provider_combo.setMinimumHeight(30)
        ai_layout.addWidget(self.provider_combo)
        
        # Stack widget to hold provider-specific settings
        self.provider_settings_stack = QStackedWidget()
        ai_layout.addWidget(self.provider_settings_stack)
        
        # --- OpenRouter settings page ---
        openrouter_page = QWidget()
        openrouter_layout = QVBoxLayout(openrouter_page)
        
        # Model selection with improved layout
        model_header = QHBoxLayout()
        model_header.addWidget(QLabel("🧠 OpenRouter Models:"))
        model_header.addStretch()
        openrouter_layout.addLayout(model_header)
        
        self.online_model_combo = QComboBox()
        self.online_model_combo.addItems(list(AVAILABLE_ONLINE_MODELS.keys()))
        self.online_model_combo.setCurrentText(DEFAULT_ONLINE_MODEL)
        self.online_model_combo.currentTextChanged.connect(self.on_online_model_changed)
        self.online_model_combo.setMinimumHeight(30)
        openrouter_layout.addWidget(self.online_model_combo)
        
        # OpenRouter API Key input
        api_key_header = QHBoxLayout()
        api_key_header.addWidget(QLabel("🔑 OpenRouter API Key:"))
        api_key_header.addStretch()
        openrouter_layout.addLayout(api_key_header)
        
        api_key_input_layout = QHBoxLayout()
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("Enter your OpenRouter API key...")
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setMinimumHeight(30)
        self.api_key_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                background-color: #2b2b2b;
                color: white;
                font-size: 11px;
            }
        """)
        api_key_input_layout.addWidget(self.api_key_input, 3)  # 3/4 of the width
        
        self.set_api_key_button = QPushButton("✓ Set")
        self.set_api_key_button.clicked.connect(self.on_set_api_key)
        self.set_api_key_button.setMinimumWidth(70)
        self.set_api_key_button.setMinimumHeight(30)
        self.set_api_key_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        api_key_input_layout.addWidget(self.set_api_key_button, 1)  # 1/4 of the width
        openrouter_layout.addLayout(api_key_input_layout)
        
        # --- Ollama settings page ---
        ollama_page = QWidget()
        ollama_layout = QVBoxLayout(ollama_page)
        
        # Ollama model selection
        ollama_model_header = QHBoxLayout()
        ollama_model_header.addWidget(QLabel("🧠 Ollama Models:"))
        ollama_model_header.addStretch()
        ollama_layout.addLayout(ollama_model_header)
        
        self.ollama_model_combo = QComboBox()
        self.ollama_model_combo.addItems(list(AVAILABLE_OLLAMA_MODELS.keys()))
        self.ollama_model_combo.setCurrentText(DEFAULT_OLLAMA_MODEL)
        self.ollama_model_combo.currentTextChanged.connect(self.on_ollama_model_changed)
        self.ollama_model_combo.setMinimumHeight(30)
        ollama_layout.addWidget(self.ollama_model_combo)
        
        # Ollama server configuration
        server_header = QHBoxLayout()
        server_header.addWidget(QLabel("🖥️ Ollama Server:"))
        server_header.addStretch()
        ollama_layout.addLayout(server_header)
        
        # Server URL layout
        server_url_layout = QHBoxLayout()
        self.server_ip_input = QLineEdit()
        self.server_ip_input.setPlaceholderText("Server IP (default: *************)")
        self.server_ip_input.setText("*************")
        self.server_ip_input.setMinimumHeight(30)
        self.server_ip_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                background-color: #2b2b2b;
                color: white;
                font-size: 11px;
            }
        """)
        server_url_layout.addWidget(self.server_ip_input, 3)
        
        self.server_port_input = QLineEdit()
        self.server_port_input.setPlaceholderText("Port (default: 11434)")
        self.server_port_input.setText("11434")
        self.server_port_input.setMaximumWidth(100)
        self.server_port_input.setMinimumHeight(30)
        self.server_port_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                background-color: #2b2b2b;
                color: white;
                font-size: 11px;
            }
        """)
        server_url_layout.addWidget(self.server_port_input, 1)
        
        self.update_server_button = QPushButton("✓ Update")
        self.update_server_button.clicked.connect(self.update_server_url)
        self.update_server_button.setMinimumWidth(70)
        self.update_server_button.setMinimumHeight(30)
        self.update_server_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        server_url_layout.addWidget(self.update_server_button, 1)
        ollama_layout.addLayout(server_url_layout)
        
        # Add both pages to stack widget
        self.provider_settings_stack.addWidget(openrouter_page)
        self.provider_settings_stack.addWidget(ollama_page)
        
        # Send button with improved styling
        self.send_scene_button = QPushButton("📷 Analyze Current Scene")
        self.send_scene_button.clicked.connect(self.send_scene_to_ai)
        self.send_scene_button.setMinimumHeight(40)
        self.send_scene_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        ai_layout.addWidget(self.send_scene_button)
        
        # AI Response display with header
        response_header = QHBoxLayout()
        response_header.addWidget(QLabel("🔍 AI Scenegraph:"))
        response_header.addStretch()
        ai_layout.addLayout(response_header)
        
        self.ai_response_display = QTextBrowser()  # Verwende QTextBrowser statt QTextEdit für Link-Unterstützung
        self.ai_response_display.setMinimumHeight(400)
        self.ai_response_display.setPlaceholderText("AI analysis results will appear here...\n\nClick 'Analyze Current Scene' or say 'test' to get started!")
        self.ai_response_display.setStyleSheet("""
            QTextBrowser {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: #2b2b2b;
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
            }
        """)
        # Enable HTML support for formatted output
        self.ai_response_display.setHtml("")
        self.ai_response_display.setAcceptRichText(True)
        self.ai_response_display.setReadOnly(True)
        # Enable anchor/link handling
        self.ai_response_display.setTextInteractionFlags(
            Qt.TextSelectableByMouse | Qt.TextSelectableByKeyboard | Qt.LinksAccessibleByMouse
        )
        self.ai_response_display.anchorClicked.connect(self.handle_anchor_click)
        self.ai_response_display.setOpenLinks(False)
        ai_layout.addWidget(self.ai_response_display)
        
        middle_layout.addWidget(ai_group)
        
        # Add some spacing at bottom
        middle_layout.addStretch()
        
        # Right Panel for Information and Log
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Scene Information
        info_group = QGroupBox("Scene Details")
        info_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        info_layout = QVBoxLayout(info_group)
        
        self.refresh_button = QPushButton("🔄 Refresh Scene Objects")
        self.refresh_button.clicked.connect(self.refresh_scene_objects_display)
        self.refresh_button.setMinimumHeight(30)
        info_layout.addWidget(self.refresh_button)
        
        self.info_display = QTextEdit()
        self.info_display.setMinimumHeight(300)
        self.info_display.setPlaceholderText("Scene object information will appear here...")
        self.info_display.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: #2b2b2b;
                color: white;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        info_layout.addWidget(self.info_display)
        
        right_layout.addWidget(info_group)
        
        # Log Display
        log_group = QGroupBox("System Log")
        log_group.setStyleSheet("font-weight: bold; font-size: 14px;")
        log_layout = QVBoxLayout(log_group)
        
        self.log_display = QTextEdit()
        self.log_display.setMinimumHeight(300)
        self.log_display.setPlaceholderText("Log messages will appear here...")
        self.log_display.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: #2b2b2b;
                color: white;
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
        """)
        log_layout.addWidget(self.log_display)
        
        right_layout.addWidget(log_group)
        
        # Robot Control Widget will be initialized after TTS worker setup
        self.object_selector_widget = None
        
        # Add panels to main layout with improved proportions
        main_layout.addWidget(left_panel, 3)      # Video + Controls (largest)
        main_layout.addWidget(middle_panel, 2)    # AI Analysis (medium) 
        main_layout.addWidget(right_panel, 2)     # Scene Info + Log (medium)
        
        # Initialize worker threads
        self.perception_thread = None
        self.worker = None
        self.whisper_thread = None
        self.whisper_worker = None
        self.openrouter_thread = None
        self.openrouter_worker = None
        self.ollama_thread = None
        self.ollama_worker = None
        self.perception_running = False  # Track perception state
        
        # Current data storage
        self.current_scene_data = None
        self.current_clean_frame = None
        self.current_annotated_frame = None
        self.current_distances_info = None
        
        # Ensure directories exist
        for directory in [IMAGE_SAVE_DIR, ANNOTATED_IMAGE_SAVE_DIR, RESPONSE_LOG_DIR,
                          OLLAMA_IMAGE_SAVE_DIR, OLLAMA_ANNOTATED_IMAGE_SAVE_DIR, OLLAMA_RESPONSE_LOG_DIR]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.log_message(f"GUI: Created directory {directory}")
        
        # Setup Whisper worker
        self.setup_whisper_worker()
        
        # Setup OpenRouter worker
        self.setup_openrouter_worker()
        
        # Setup Ollama worker
        self.setup_ollama_worker()
        
        # Initialize YOLOe prompt visibility based on current model
        current_model = self.model_combo.currentText()
        is_yoloe_model = 'yoloe' in current_model.lower()
        is_prompt_free = '-pf' in current_model.lower()
        self.yoloe_prompt_group.setVisible(is_yoloe_model and not is_prompt_free)
        
        # Add TTS checkbox to AI Scenegraph section (same style as Save Audio Files checkbox)
        self.cb_enable_tts = QCheckBox("🔊 Text-to-Speech")
        self.cb_enable_tts.setChecked(False)
        self.cb_enable_tts.stateChanged.connect(self.on_tts_enabled_changed)
        self.cb_enable_tts.setToolTip("Vorlesen der LLM-Antworten mit Thorsten VITS")
        self.cb_enable_tts.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 13px;
                font-weight: bold;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #555;
                border-radius: 4px;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                background-color: #2196F3;
                border: 2px solid #2196F3;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDUuNSAxMEwxMS41IDQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #666;
                background-color: #3b3b3b;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #1976D2;
                border: 2px solid #1976D2;
            }
        """)
        response_header.addWidget(self.cb_enable_tts)
        
        # Initialize TTS worker
        self.tts_thread = None
        self.tts_worker = None
        self.setup_tts_worker()

        # Initialize Robot Control Widget after TTS worker is ready
        self._setup_robot_control_widget(right_layout)

        # Initialize Voice Intent System
        self._setup_voice_intent_system()

        # Voice input mode variables
        self.current_voice_mode = "Push-to-Talk"
        self.is_push_to_talk_active = False
        self.space_key_pressed = False

        # Initialize Conversation Context
        self.conversation_context = ConversationContext()

    @Slot(str)
    def on_model_changed(self, model_name):
        """Handle model selection change in GUI"""
        if model_name in AVAILABLE_YOLO_MODELS:
            self.current_model = model_name
            model_path = AVAILABLE_YOLO_MODELS[model_name]
            
            # Update window title
            # self.setWindowTitle(f"ZED YOLO Object Detection with Whisper STT - {model_name}")
            self.setWindowTitle(f"pAIrSEEption")
            # Log the change
            self.log_message(f"GUI: Model changed to {model_name} ({model_path})")
            
            # Show/hide YOLOe prompt group based on model type
            is_yoloe_model = 'yoloe' in model_name.lower()
            is_prompt_free = '-pf' in model_name.lower()
            self.yoloe_prompt_group.setVisible(is_yoloe_model and not is_prompt_free)
            
            # If worker is running, signal model change
            if self.worker and self.perception_thread and self.perception_thread.isRunning():
                self.model_changed.emit(model_path)
            else:
                self.log_message("GUI: Model will be applied when perception starts")

    def setup_whisper_worker(self):
        self.whisper_thread = QThread(self)
        current_model = self.whisper_model_combo.currentText() if hasattr(self, 'whisper_model_combo') else WHISPER_MODEL_NAME
        self.whisper_worker = WhisperWorker(model_size=current_model)
        self.whisper_worker.moveToThread(self.whisper_thread)
        
        # Apply current GUI settings to worker
        if hasattr(self, 'language_combo'):
            language_text = self.language_combo.currentText()
            language_code = language_text.split(" ")[0] if language_text != "auto (Auto-detect)" else None
            self.whisper_worker.set_language(language_code)
        
        if hasattr(self, 'duration_combo'):
            duration = float(self.duration_combo.currentText().replace("s", ""))
            self.whisper_worker.set_recording_duration(duration)
        
        if hasattr(self, 'save_audio_checkbox'):
            enabled = self.save_audio_checkbox.isChecked()
            self.whisper_worker.set_save_audio_files(enabled)
        
        self.whisper_worker.transcription_ready.connect(self.update_transcription_display)
        self.whisper_worker.status_update.connect(self.log_message)
        self.whisper_thread.start()
        self.log_message("GUI: Whisper worker thread started.")
        QTimer.singleShot(200, lambda: QMetaObject.invokeMethod(self.whisper_worker, "load_whisper_model", Qt.QueuedConnection))

    def toggle_perception(self):
        """Toggle perception start/stop"""
        if not self.perception_running:
            self.start_worker_thread()
        else:
            self.stop_worker_thread()

    def stop_worker_thread(self):
        """Stop the perception worker thread"""
        if self.perception_thread and self.perception_thread.isRunning():
            self.log_message("GUI: Stopping perception thread...")
            self.stop_perception_signal.emit()
            self.perception_thread.quit()
            self.perception_thread.wait()
            
        self.perception_running = False
        self.start_button.setText("Start pAIrSEEption")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.log_message("GUI: Perception stopped.")

    @Slot(str)
    def on_whisper_model_changed(self, model_name):
        """Handle whisper model selection change"""
        self.log_message(f"GUI: Whisper model changed to {model_name}")
        # Restart whisper worker with new model
        if self.whisper_thread and self.whisper_thread.isRunning():
            if self.whisper_worker and self.whisper_worker.is_recording:
                QMetaObject.invokeMethod(self.whisper_worker, "stop_and_transcribe", Qt.BlockingQueuedConnection)
            
            # Clean up old model to free memory
            if hasattr(self.whisper_worker, 'model') and self.whisper_worker.model is not None:
                del self.whisper_worker.model
                self.whisper_worker.model = None
                # Clear GPU cache if torch is available
                try:
                    if torch is not None:
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()  # Ensure all operations complete
                        self.log_message("GUI: GPU memory cleared after Whisper model change")
                except:
                    pass
            
            self.whisper_thread.quit()
            self.whisper_thread.wait()
        
        # Start with new model
        self.setup_whisper_worker()

        # Trigger model loading after worker is set up
        QTimer.singleShot(200, lambda: QMetaObject.invokeMethod(self.whisper_worker, "load_whisper_model", Qt.QueuedConnection))

    @Slot(bool)
    def on_listen_button_clicked(self, checked):
        # Listen button is hidden in Push-to-Talk mode
        # This callback is kept for potential future use
        pass
            
    def reset_listen_button(self):
        self.listen_button.setChecked(False)
        self.listen_button.setText("🎤 Listen and Transcribe")

    @Slot(str)
    def update_transcription_display(self, text):
        current_time = time.strftime("%H:%M:%S", time.localtime())
        self.transcription_display.append(f"[{current_time}] {text}")

        # Process voice commands with intent recognition
        self.process_voice_command(text)

        # Legacy keyword detection is now handled by voice intent parser
        # No additional processing needed here

    @Slot(str)
    def on_voice_input_mode_changed(self, mode):
        """Handle voice input mode selection change"""
        self.current_voice_mode = mode
        self.log_message(f"GUI: Voice input mode changed to {mode}")

        # Only Push-to-Talk mode available now
        self.voice_status_label.setText("Mode: Push-to-Talk (Button or Spacebar)")
        self.push_to_talk_button.setVisible(True)
        self.listen_button.setVisible(False)

    @Slot(str)
    def on_language_changed(self, language_text):
        """Handle language selection change for Whisper"""
        language_code = language_text.split(" ")[0]  # Extract code from "de (German)" format
        if language_code == "auto":
            language_code = None  # Let Whisper auto-detect

        self.log_message(f"GUI: Whisper language changed to {language_text}")
        if self.whisper_worker:
            self.whisper_worker.set_language(language_code)

    # Duration callback removed - Push-to-Talk controls duration manually

    @Slot(int)
    def on_save_audio_changed(self, state):
        """Handle audio saving checkbox change - only for Whisper input audio"""
        enabled = state == Qt.CheckState.Checked.value
        self.log_message(f"GUI: Whisper audio file saving {'enabled' if enabled else 'disabled'}")

        # Update Whisper worker (for input audio recording only)
        if self.whisper_worker:
            self.whisper_worker.set_save_audio_files(enabled)

    def on_display_option_changed(self):
        self.send_initial_checkbox_states_to_worker()
        
    @Slot(int)
    def on_log_performance_changed(self, state):
        """Handle log performance checkbox change"""
        enabled = state == Qt.CheckState.Checked.value
        self.log_message(f"GUI: Performance logging {'enabled' if enabled else 'disabled'}")
        if self.worker and self.perception_thread and self.perception_thread.isRunning():
            QMetaObject.invokeMethod(self.worker, "set_log_performance", Qt.QueuedConnection, Q_ARG(bool, enabled))

    def start_worker_thread(self):
        if self.perception_thread and self.perception_thread.isRunning():
            self.log_message("GUI: Stopping current perception thread...")
            self.stop_perception_signal.emit()
            self.perception_thread.quit()
            self.perception_thread.wait()
        
        self.log_message(f"GUI: Starting perception thread...")
        self.perception_thread = QThread(self)
        self.worker = PerceptionWorker()
        self.worker.moveToThread(self.perception_thread)
        
        self.worker.frame_ready.connect(self.update_frame)
        self.worker.status_update.connect(self.log_message)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.scene_data_ready.connect(self.handle_scene_data_update)
        self.worker.stats_update.connect(self.update_statistics_display)
        
        self.confidence_threshold_changed.connect(self.worker.set_confidence_threshold)
        self.stop_perception_signal.connect(self.worker.stop, Qt.DirectConnection)
        self.show_yolo_plot_bboxes_labels_conf_changed.connect(self.worker.set_show_yolo_plot_bboxes_labels_conf)
        self.show_yolo_plot_masks_changed.connect(self.worker.set_show_yolo_plot_masks)
        self.show_zed_id_changed.connect(self.worker.set_show_zed_id)
        self.show_zed_pos_changed.connect(self.worker.set_show_zed_pos)
        self.show_zed_speed_changed.connect(self.worker.set_show_zed_speed)
        self.model_changed.connect(self.worker.set_model_path)
        
        # Connect worker's model_changed signal to update GUI
        self.worker.model_changed.connect(self.on_worker_model_changed)
        
        self.perception_thread.started.connect(self.worker.run)
        self.perception_thread.finished.connect(self.on_thread_finished)
        
        self.perception_thread.start()
        self.log_message("GUI: Perception thread start requested.")
        
        # Update button state
        self.perception_running = True
        self.start_button.setText("Stop pAIrSEEption")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        
        # Send initial model selection to worker
        if self.current_model and self.current_model in AVAILABLE_YOLO_MODELS:
            current_model_path = AVAILABLE_YOLO_MODELS[self.current_model]
            QTimer.singleShot(50, lambda: self.model_changed.emit(current_model_path))
        else:
            self.log_message("⚠️  No valid YOLO model selected. Please configure models in config.yaml")
        QTimer.singleShot(100, self.send_initial_checkbox_states_to_worker)

    @Slot(str)
    def on_worker_model_changed(self, model_path):
        """Handle successful model change from worker"""
        # Find the model name from the path
        model_name = None
        for name, path in AVAILABLE_YOLO_MODELS.items():
            if path == model_path:
                model_name = name
                break
        
        if model_name:
            self.current_model = model_name
            # self.setWindowTitle(f"ZED YOLO Object Detection with Whisper STT - {model_name}")
            self.setWindowTitle(f"pAIrSEEption")
            self.log_message(f"GUI: Model successfully changed to {model_name}")
            
            # Update combo box if it doesn't match (avoid recursion)
            if self.model_combo.currentText() != model_name:
                self.model_combo.blockSignals(True)
                self.model_combo.setCurrentText(model_name)
                self.model_combo.blockSignals(False)

    def update_object_selector(self, object_data_list):
        """
        Updates the object selector widget with currently detected objects
        """
        if hasattr(self, 'object_selector_widget') and self.object_selector_widget and object_data_list:
            self.object_selector_widget.update_objects(object_data_list)

    @Slot(list, np.ndarray, list)
    def handle_scene_data_update(self, scene_data_list, clean_frame, distances):
        self.current_scene_data = scene_data_list
        self.current_clean_frame = clean_frame
        self.current_distances_info = distances
        
        # Update the object selector with the new scene data
        self.update_object_selector(scene_data_list)

    @Slot()
    def refresh_scene_objects_display(self):
        display_text_parts = ["--- Current Scene Objects ---"]
        
        if hasattr(self, 'current_scene_data') and self.current_scene_data:
            for obj_data in self.current_scene_data:
                obj_id = obj_data.get('object_id', 'N/A')
                class_name = obj_data.get('class_name', 'N/A')
                position = obj_data.get('position', [])
                speed = obj_data.get('speed', 0)
                confidence = obj_data.get('confidence', 0)
                
                pos_str = f"[{position[0]:.2f}, {position[1]:.2f}, {position[2]:.2f}]" if position and len(position) == 3 else "[N/A]"
                display_text_parts.append(f"ID:{obj_id} {class_name} Pos:{pos_str} Speed:{speed:.1f}m/s Conf:{confidence:.0f}%")
        else:
            display_text_parts.append("No objects currently tracked.")

        display_text_parts.append("\n--- Relative Distances ---")
        if hasattr(self, 'current_distances_info') and self.current_distances_info:
            for dist_data in self.current_distances_info:
                id1 = dist_data.get('id1', 'N/A')
                class1 = dist_data.get('class1', 'N/A')
                id2 = dist_data.get('id2', 'N/A')
                class2 = dist_data.get('class2', 'N/A')
                distance = dist_data.get('distance', 0)
                display_text_parts.append(f"ID:{id1}({class1}) <-> ID:{id2}({class2}): {distance:.2f}m")
        else:
            display_text_parts.append("No distance calculations available.")

        final_text = "\n".join(display_text_parts)
        self.info_display.setText(final_text)
        self.info_display.moveCursor(QTextCursor.MoveOperation.Start)

    @Slot()
    def send_scene_to_ai(self):
        """Send current scene to selected AI provider"""
        current_provider = self.provider_combo.currentText()
        self.log_message(f"GUI: Sending scene to {current_provider}...")
        
        if hasattr(self, 'current_scene_data') and self.current_scene_data is not None and \
           hasattr(self, 'current_clean_frame') and self.current_clean_frame is not None and \
           hasattr(self, 'current_annotated_frame') and self.current_annotated_frame is not None:
            
            if current_provider == "OpenRouter":
                # Use OpenRouter API
                if self.openrouter_worker and self.openrouter_thread and self.openrouter_thread.isRunning():
                    # Send via signal to ensure async operation
                    self.openrouter_scene_request.emit(self.current_clean_frame, 
                                                      self.current_annotated_frame, 
                                                      self.current_scene_data)
                else:
                    self.log_message("GUI Error: OpenRouter worker not available.")
            else:
                # Use Ollama API
                if self.ollama_worker and self.ollama_thread and self.ollama_thread.isRunning():
                    # Send via signal to ensure async operation
                    self.ollama_scene_request.emit(self.current_clean_frame, 
                                                  self.current_annotated_frame, 
                                                  self.current_scene_data)
                else:
                    self.log_message("GUI Error: Ollama worker not available.")
        else:
            self.log_message("GUI Error: Scene data, clean frame, or annotated frame not available.")
    
    def process_voice_command(self, text):
        """Process voice command with intent recognition and context awareness"""
        if not hasattr(self, 'voice_intent_parser') or not self.voice_intent_parser:
            return

        try:
            # Check for confirmation responses first (Ja/Nein)
            if hasattr(self, 'conversation_context') and self.conversation_context.is_confirmation_response(text):
                pending_command = self.conversation_context.get_pending_confirmation()
                if pending_command:
                    if self.conversation_context.is_positive_confirmation(text):
                        # User confirmed - execute the pending command
                        self.log_message(f"GUI: User confirmed suggestion, executing: {pending_command}")
                        self.conversation_context.clear_pending_confirmation()

                        # Execute the confirmed command
                        if self.voice_intent_executor:
                            response = self.voice_intent_executor.execute(pending_command)
                            if response:
                                self.log_message(f"GUI: Confirmed command executed: {response}")

                        # Speak confirmation if TTS is enabled
                        if self.tts_worker and self.cb_enable_tts.isChecked():
                            QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text",
                                                   Qt.QueuedConnection, Q_ARG(str, "Verstanden, führe Befehl aus."))
                        return
                    else:
                        # User declined - clear pending command
                        self.log_message("GUI: User declined suggestion")
                        self.conversation_context.clear_pending_confirmation()
                        response = "Verstanden, Befehl abgebrochen."

                        # Speak decline response if TTS is enabled
                        if self.tts_worker and self.cb_enable_tts.isChecked():
                            QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text",
                                                   Qt.QueuedConnection, Q_ARG(str, response))
                        return
                else:
                    # No pending command but user said yes/no
                    response = "Es gibt keinen ausstehenden Befehl zum Bestätigen."
                    if self.tts_worker and self.cb_enable_tts.isChecked():
                        QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text",
                                               Qt.QueuedConnection, Q_ARG(str, response))
                    return

            # Apply conversation context to resolve references
            if hasattr(self, 'conversation_context'):
                resolved_text = self.conversation_context.resolve_references(text)
                if resolved_text != text:
                    self.log_message(f"GUI: Context resolved '{text}' -> '{resolved_text}'")
                    text = resolved_text

            # Parse the voice command
            intent_data = self.voice_intent_parser.parse(text)
            self.log_message(f"GUI: Voice intent parsed: {intent_data['intent']}")

            response = None

            if intent_data["intent"] == "chat":
                # Handle chat mode - send to LLM without image
                self.log_message(f"GUI: Chat mode - text: {intent_data['text']}")
                response = self.process_chat_request(intent_data['text'])
                # No fallback response needed - actual response will come via signal
            else:
                # Execute robot/system command
                if self.voice_intent_executor:
                    response = self.voice_intent_executor.execute(intent_data)
                    if response:
                        self.log_message(f"GUI: Voice command executed: {response}")

            # Add interaction to conversation context (only for non-chat responses)
            if hasattr(self, 'conversation_context') and response and intent_data["intent"] != "chat":
                self.conversation_context.add_interaction(text, response, intent_data)

                # Log context info for debugging
                context_info = self.conversation_context.get_context_info()
                self.log_message(f"GUI: Context updated - Topic: {context_info['current_topic']}, "
                               f"Active nav: {context_info['active_navigation'] is not None}")

            # Speak the response if TTS is enabled (only for non-chat responses and if response is not None)
            if response and self.tts_worker and self.cb_enable_tts.isChecked() and intent_data["intent"] != "chat":
                # Use different TTS methods based on intent type
                if intent_data["intent"] == "analyze_scene":
                    # Scene analysis uses temporary audio (like chat)
                    QMetaObject.invokeMethod(self.tts_worker, "speak_text",
                                           Qt.QueuedConnection, Q_ARG(str, response))
                else:
                    # Navigation commands use pre-generated files when available
                    QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text",
                                           Qt.QueuedConnection, Q_ARG(str, response))

        except Exception as e:
            error_msg = f"Fehler bei Sprachkommando: {str(e)}"
            self.log_message(f"GUI Error: {error_msg}")

            # Speak error message if TTS is enabled
            if self.tts_worker and self.cb_enable_tts.isChecked():
                QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text",
                                       Qt.QueuedConnection, Q_ARG(str, error_msg))

    def process_chat_request(self, text):
        """Process chat request - send text-only question to LLM"""
        try:
            current_provider = self.provider_combo.currentText()

            # Create chat prompt for German conversation
            chat_prompt = f"""Du bist ein hilfsreicher Assistent für einen Roboter-Benutzer.
Beantworte die folgende Frage kurz und präzise auf Deutsch.
Du kannst über Alltagsthemen, Wetter, Mathematik, Zeit, etc. sprechen.

Frage: {text}

Antwort:"""

            self.log_message(f"GUI: Sending chat request to {current_provider}: {text}")

            if current_provider == "OpenRouter":
                # Send to OpenRouter for chat
                if self.openrouter_worker and self.openrouter_thread and self.openrouter_thread.isRunning():
                    # Use a simple text-only request
                    self.send_chat_to_openrouter(chat_prompt)
                    return None  # No temporary response - wait for actual response
                else:
                    self.log_message("GUI Error: OpenRouter worker not available for chat.")
                    return None
            else:
                # Send to Ollama for chat
                if self.ollama_worker and self.ollama_thread and self.ollama_thread.isRunning():
                    self.send_chat_to_ollama(chat_prompt)
                    return None  # No temporary response - wait for actual response
                else:
                    self.log_message("GUI Error: Ollama worker not available for chat.")
                    return None

        except Exception as e:
            self.log_message(f"GUI Error: Failed to process chat request: {e}")
            return f"Fehler bei Chat-Anfrage: {str(e)}"

    def send_chat_to_openrouter(self, prompt):
        """Send text-only chat request to OpenRouter"""
        if self.openrouter_worker and self.openrouter_thread and self.openrouter_thread.isRunning():
            QMetaObject.invokeMethod(self.openrouter_worker, "process_chat_request",
                                   Qt.QueuedConnection, Q_ARG(str, prompt))
        else:
            self.log_message("GUI Error: OpenRouter worker not available for chat.")

    def send_chat_to_ollama(self, prompt):
        """Send text-only chat request to Ollama"""
        if self.ollama_worker and self.ollama_thread and self.ollama_thread.isRunning():
            QMetaObject.invokeMethod(self.ollama_worker, "process_chat_request",
                                   Qt.QueuedConnection, Q_ARG(str, prompt))
        else:
            self.log_message("GUI Error: Ollama worker not available for chat.")

    @Slot(str)
    def handle_chat_response(self, response_text):
        """Handle chat response from LLM - display only (TTS handled by display_ai_response)"""
        try:
            # Log the response
            self.log_message(f"GUI: Chat response received: {response_text[:100]}...")

            # Display in AI response area (this will also handle TTS automatically)
            self.display_ai_response(response_text)

        except Exception as e:
            error_msg = f"Fehler bei Chat-Antwort: {str(e)}"
            self.log_message(f"GUI Error: {error_msg}")

            # Display error message (this will also handle TTS automatically)
            self.display_ai_response(error_msg)



    def setup_openrouter_worker(self):
        """Setup the OpenRouter worker thread"""
        self.openrouter_thread = QThread(self)
        self.openrouter_worker = OpenRouterWorker()
        self.openrouter_worker.moveToThread(self.openrouter_thread)
        
        # Connect signals
        self.openrouter_worker.response_ready.connect(self.display_ai_response)
        self.openrouter_worker.chat_response_ready.connect(self.handle_chat_response)
        self.openrouter_worker.status_update.connect(self.log_message)
        self.online_model_changed.connect(self.openrouter_worker.set_online_model)
        
        # Use specific signals for the OpenRouter worker
        self.openrouter_scene_request.connect(self.openrouter_worker.process_annotated_scene_request)
        
        # Ensure directories exist
        for directory in [IMAGE_SAVE_DIR, ANNOTATED_IMAGE_SAVE_DIR, RESPONSE_LOG_DIR]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.log_message(f"GUI: Created directory {directory}")
        
        self.openrouter_thread.start()
        self.log_message("GUI: OpenRouter worker thread started.")

    @Slot(str)
    def on_online_model_changed(self, model_name):
        """Handle online model selection change"""
        if model_name in AVAILABLE_ONLINE_MODELS:
            self.log_message(f"GUI: Online model changed to {model_name}")
            if self.openrouter_worker and self.openrouter_thread and self.openrouter_thread.isRunning():
                self.online_model_changed.emit(model_name)
    
    @Slot(str)
    def on_ollama_model_changed(self, model_name):
        """Handle Ollama model selection change"""
        if model_name in AVAILABLE_OLLAMA_MODELS:
            self.log_message(f"GUI: Ollama model changed to {model_name}")
            if self.ollama_worker and self.ollama_thread and self.ollama_thread.isRunning():
                self.ollama_model_changed.emit(model_name)

    @Slot()
    def on_set_api_key(self):
        """Handle API key setting"""
        api_key = self.api_key_input.text().strip()
        if api_key:
            if self.openrouter_worker and self.openrouter_thread and self.openrouter_thread.isRunning():
                # Update the API key in the worker
                QMetaObject.invokeMethod(self.openrouter_worker, "set_api_key", Qt.QueuedConnection, Q_ARG(str, api_key))
                self.log_message(f"GUI: OpenRouter API key updated")
                self.api_key_input.clear()
                self.api_key_input.setPlaceholderText("API key set successfully!")
            else:
                self.log_message("GUI Error: OpenRouter worker not available.")
        else:
            self.log_message("GUI Warning: Please enter a valid API key.")

    @Slot(str)
    def display_ai_response(self, response):
        """Display AI response in the GUI"""
        current_time = time.strftime("%H:%M:%S", time.localtime())
        
        # Determine the current model based on the selected provider
        current_provider = self.provider_combo.currentText()
        if current_provider == "OpenRouter":
            model_name = self.online_model_combo.currentText()
            model_color = "#3498db"  # Blue for OpenRouter
        else:  # Ollama
            model_name = self.ollama_model_combo.currentText()
            model_color = "#e74c3c"  # Red for Ollama
        
        # Format the output with HTML for color support
        formatted_output = f'<span style="color:#666;">[{current_time}]</span> <span style="color:{model_color};font-weight:bold;">{model_name}</span>: {response}'
        
        # Add replay button if TTS is enabled
        if self.cb_enable_tts.isChecked():
            # Create a unique ID for this response
            response_id = generate_request_id()
            
            # Store the response for replay
            if not hasattr(self, 'tts_responses'):
                self.tts_responses = {}
            self.tts_responses[response_id] = response
            
            # Add a replay button after the response
            formatted_output += f'<br><span style="display:inline-block;margin-top:5px;"><a href="replay:{response_id}" style="text-decoration:none;"><span style="background-color:#4CAF50;color:white;border-radius:4px;padding:2px 8px;font-size:12px;">🔊 Replay</span></a></span>'
        
        self.ai_response_display.append(formatted_output)
        
        # Speak the response if TTS is enabled
        if self.tts_worker and self.cb_enable_tts.isChecked():
            # Check if this is a scene analysis response (has Request ID)
            import re
            if re.search(r'\[Request ID: [0-9_]+\]', response):
                # Scene analysis - save audio file
                QMetaObject.invokeMethod(self.tts_worker, "speak_scene_analysis_text", Qt.QueuedConnection, Q_ARG(str, response))
            else:
                # Normal chat - temporary audio only
                QMetaObject.invokeMethod(self.tts_worker, "speak_text", Qt.QueuedConnection, Q_ARG(str, response))
        
        self.log_message(f"GUI: AI response received from {model_name}")
    
    def handle_anchor_click(self, url):
        """Handle anchor clicks in the response display"""
        url_str = url.toString()
        if url_str.startswith('replay:'):
            response_id = url_str[7:]  # Remove 'replay:' prefix
            if hasattr(self, 'tts_responses') and response_id in self.tts_responses:
                response_text = self.tts_responses[response_id]
                self.replay_tts(response_text)
        
    def replay_tts(self, response_text):
        """Replay TTS for a specific response"""
        if self.tts_worker:
            # Extract request ID from the text if available
            import re
            request_id_match = re.search(r'\[Request ID: ([0-9_]+)\]', response_text)
            
            if request_id_match and hasattr(self.tts_worker, 'response_audio_files'):
                request_id = request_id_match.group(1)
                if request_id in self.tts_worker.response_audio_files:
                    # Use the existing audio file
                    filepath = self.tts_worker.response_audio_files[request_id]
                    if os.path.exists(filepath):
                        self.log_message(f"GUI: Replaying saved TTS audio for response {request_id}")
                        QMetaObject.invokeMethod(self.tts_worker, "play_audio_file", Qt.QueuedConnection, Q_ARG(str, filepath))
                        return
            
            # Fall back to generating new audio if file not found
            self.log_message("GUI: Replaying TTS for response")
            QMetaObject.invokeMethod(self.tts_worker, "speak_text", Qt.QueuedConnection, Q_ARG(str, response_text))

    def send_initial_checkbox_states_to_worker(self):
        if self.worker and self.perception_thread and self.perception_thread.isRunning():
            self.show_yolo_plot_bboxes_labels_conf_changed.emit(self.cb_show_yolo_plot_bbox_label_conf.isChecked())
            self.show_yolo_plot_masks_changed.emit(self.cb_show_yolo_masks.isChecked())
            self.show_zed_id_changed.emit(self.cb_show_zed_id.isChecked())
            self.show_zed_pos_changed.emit(self.cb_show_zed_pos.isChecked())
            self.show_zed_speed_changed.emit(self.cb_show_zed_speed.isChecked())
            
            # Send performance logging state
            QMetaObject.invokeMethod(self.worker, "set_log_performance", 
                                    Qt.QueuedConnection, 
                                    Q_ARG(bool, self.cb_log_performance.isChecked()))
        else:
            self.log_message("GUI Warning: Worker not ready for initial states. Retrying...")
            QTimer.singleShot(500, self.send_initial_checkbox_states_to_worker)

    @Slot()
    def on_worker_finished(self):
        self.log_message("GUI: Worker signaled its loop has finished. Requesting QThread to quit.")
        if self.perception_thread:
            self.perception_thread.quit()
        
        # Reset button state
        self.perception_running = False
        self.start_button.setText("Start pAIrSEEption")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

    @Slot()
    def on_thread_finished(self):
        self.log_message("GUI: Perception QThread finished.")
        if self.worker:
            self.worker.deleteLater()
            self.worker = None
        if self.perception_thread:
            self.perception_thread.deleteLater()
            self.perception_thread = None

    @Slot(int)
    def confidence_slider_changed(self, value):
        threshold = value / 100.0
        self.conf_value_label.setText(f"{threshold:.2f}")
        self.confidence_threshold_changed.emit(threshold)
    
    @Slot(str)
    def log_message(self, message):
        current_time = time.strftime("%H:%M:%S", time.localtime())
        log_entry = f"[{current_time}] {message}"
        
        self.log_display.append(log_entry)
        if self.log_display.document().lineCount() > 200:
            cursor = self.log_display.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.Start)
            lines_to_remove = self.log_display.document().lineCount() - 200
            for _ in range(lines_to_remove):
                cursor.movePosition(QTextCursor.MoveOperation.Down, QTextCursor.MoveMode.KeepAnchor)
            cursor.removeSelectedText()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.log_display.setTextCursor(cursor)
        self.log_display.ensureCursorVisible()

    @Slot(np.ndarray)
    def update_frame(self, frame_bgr_annotated):
        try:
            # Store the current annotated frame for potential saving
            self.current_annotated_frame = frame_bgr_annotated.copy() if frame_bgr_annotated is not None else None
            
            if frame_bgr_annotated is None or frame_bgr_annotated.size == 0:
                self.video_label.setText("Error: Received empty frame")
                return
            h, w, ch = frame_bgr_annotated.shape
            if h == 0 or w == 0:
                self.video_label.setText("Error: Frame has zero dimension")
                return
            bytes_per_line = ch * w
            qt_format = QImage.Format_RGB888
            img_data_for_qimage = frame_bgr_annotated.data
            if ch == 3:
                rgb_frame = cv2.cvtColor(frame_bgr_annotated, cv2.COLOR_BGR2RGB)
                img_data_for_qimage = rgb_frame.data
                qt_format = QImage.Format_RGB888
            elif ch == 4:
                rgba_frame = cv2.cvtColor(frame_bgr_annotated, cv2.COLOR_BGRA2RGBA)
                img_data_for_qimage = rgba_frame.data
                qt_format = QImage.Format_RGBA8888
            else:
                self.log_message(f"GUI Warning: Frame with unhandled channel count {ch}. Attempting Grayscale.")
                if frame_bgr_annotated.ndim == 2:
                    img_data_for_qimage = frame_bgr_annotated.data
                    qt_format = QImage.Format_Grayscale8
                    bytes_per_line = w
                elif frame_bgr_annotated.ndim == 3 and ch > 0:
                    gray_frame = frame_bgr_annotated[:,:,0].copy()
                    img_data_for_qimage = gray_frame.data
                    qt_format = QImage.Format_Grayscale8
                    bytes_per_line = w
                else:
                    self.video_label.setText(f"Error: Cannot handle frame shape {frame_bgr_annotated.shape}")
                    return
            qt_image = QImage(img_data_for_qimage, w, h, bytes_per_line, qt_format)
            if qt_image.isNull():
                self.video_label.setText("Error: QImage conversion failed.")
                return
            pixmap = QPixmap.fromImage(qt_image)
            self.video_label.setPixmap(pixmap.scaled(self.video_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        except Exception as e:
            print(f"GUI Error updating frame: {e}")
            self.log_message(f"GUI Error updating frame: {e}")
            self.video_label.setText("Error displaying video frame")

    def keyPressEvent(self, event):
        """Handle keyboard events for Push-to-Talk with Spacebar"""
        if event.key() == Qt.Key_Space and not event.isAutoRepeat():
            if not self.space_key_pressed:
                self.space_key_pressed = True
                self.start_push_to_talk_recording()
        super().keyPressEvent(event)

    def keyReleaseEvent(self, event):
        """Handle keyboard release events for Push-to-Talk with Spacebar"""
        if event.key() == Qt.Key_Space and not event.isAutoRepeat():
            if self.space_key_pressed:
                self.space_key_pressed = False
                self.stop_push_to_talk_recording()
        super().keyReleaseEvent(event)

    def start_push_to_talk_recording(self):
        """Start Push-to-Talk recording"""
        if not self.is_push_to_talk_active:
            self.is_push_to_talk_active = True
            self.voice_status_label.setText("🔴 Recording... (Release button/spacebar to stop)")
            self.push_to_talk_button.setText("🔴 Recording...")
            self.log_message("GUI: Push-to-Talk recording started")
            QMetaObject.invokeMethod(self.whisper_worker, "start_recording_and_transcribe", Qt.QueuedConnection)

    def stop_push_to_talk_recording(self):
        """Stop Push-to-Talk recording"""
        if self.is_push_to_talk_active:
            self.is_push_to_talk_active = False
            self.voice_status_label.setText("Mode: Push-to-Talk (Button or Spacebar)")
            self.push_to_talk_button.setText("🎤 Hold to Talk")
            self.log_message("GUI: Push-to-Talk recording stopped")
            # Note: WhisperWorker will handle the actual stopping

    # VAD methods removed - only Push-to-Talk mode available

    def closeEvent(self, event):
        self.log_message("GUI: Close event triggered. Closing application...")
        
        # Shut down robot control if available
        if hasattr(self, 'object_selector_widget') and self.object_selector_widget:
            try:
                self.object_selector_widget.shutdown()
                self.log_message("GUI: Robot control shutdown complete")
            except Exception as e:
                self.log_message(f"GUI Error: Failed to shutdown robot control: {e}")
        
        if self.perception_thread and self.perception_thread.isRunning():
            self.log_message("GUI: Sending stop signal to perception worker...")
            if self.worker:
                self.worker.stop()
            self.perception_thread.quit()
            self.log_message("GUI: Waiting for perception thread to finish...")
            if not self.perception_thread.wait(3000):
                self.log_message("GUI Warning: Perception thread did not finish in time.")
            else:
                self.log_message("GUI: Perception thread finished gracefully.")
        else:
            self.log_message("GUI: Perception thread not running or already finished.")

        if self.whisper_thread and self.whisper_thread.isRunning():
            self.log_message("GUI: Stopping Whisper worker thread...")
            if self.whisper_worker and self.whisper_worker.is_recording:
                QMetaObject.invokeMethod(self.whisper_worker, "stop_and_transcribe", Qt.BlockingQueuedConnection)
            self.whisper_thread.quit()
            if not self.whisper_thread.wait(3000):
                self.log_message("GUI Warning: Whisper thread did not finish in time.")
            else:
                self.log_message("GUI: Whisper thread finished.")

        if self.openrouter_thread and self.openrouter_thread.isRunning():
            self.log_message("GUI: Stopping OpenRouter worker thread...")
            if self.openrouter_worker:
                QMetaObject.invokeMethod(self.openrouter_worker, "stop", Qt.QueuedConnection)
            self.openrouter_thread.quit()
            if not self.openrouter_thread.wait(3000):
                self.log_message("GUI Warning: OpenRouter thread did not finish in time.")
            else:
                self.log_message("GUI: OpenRouter thread finished.")
        
        if self.ollama_thread and self.ollama_thread.isRunning():
            self.log_message("GUI: Stopping Ollama worker thread...")
            if self.ollama_worker:
                QMetaObject.invokeMethod(self.ollama_worker, "stop", Qt.QueuedConnection)
            self.ollama_thread.quit()
            if not self.ollama_thread.wait(3000):
                self.log_message("GUI Warning: Ollama thread did not finish in time.")
            else:
                self.log_message("GUI: Ollama thread finished.")
        
        if self.tts_thread and self.tts_thread.isRunning():
            self.log_message("GUI: Stopping TTS worker thread...")
            if self.tts_worker:
                QMetaObject.invokeMethod(self.tts_worker, "stop", Qt.QueuedConnection)
            self.tts_thread.quit()
            if not self.tts_thread.wait(3000):
                self.log_message("GUI Warning: TTS thread did not finish in time.")
            else:
                self.log_message("GUI: TTS thread finished.")
        
        print("Exiting application.")
        event.accept()

    @Slot(str)
    def on_provider_changed(self, provider):
        """Handle provider selection change"""
        self.log_message(f"GUI: AI Provider changed to {provider}")
        
        # Update stack widget to show appropriate settings
        if provider == "OpenRouter":
            self.provider_settings_stack.setCurrentIndex(0)
        else:  # Ollama
            self.provider_settings_stack.setCurrentIndex(1)

    def setup_ollama_worker(self):
        """Setup the Ollama worker thread"""
        self.ollama_thread = QThread(self)
        self.ollama_worker = OllamaWorker()
        self.ollama_worker.moveToThread(self.ollama_thread)
        
        # Connect signals
        self.ollama_worker.response_ready.connect(self.display_ai_response)
        self.ollama_worker.chat_response_ready.connect(self.handle_chat_response)
        self.ollama_worker.status_update.connect(self.log_message)
        self.ollama_model_changed.connect(self.ollama_worker.set_ollama_model)
        
        # Use specific signals for the Ollama worker
        self.ollama_scene_request.connect(self.ollama_worker.process_annotated_scene_request)
        
        # Ensure directories exist
        for directory in [OLLAMA_IMAGE_SAVE_DIR, OLLAMA_ANNOTATED_IMAGE_SAVE_DIR, OLLAMA_RESPONSE_LOG_DIR]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.log_message(f"GUI: Created directory {directory}")
        
        self.ollama_thread.start()
        self.log_message("GUI: Ollama worker thread started.")
    
    def update_server_url(self):
        """Update the Ollama server URL"""
        server_ip = self.server_ip_input.text().strip()
        server_port = self.server_port_input.text().strip()
        
        if not server_ip:
            server_ip = "*************"  # Default IP
            self.server_ip_input.setText(server_ip)
        
        if not server_port:
            server_port = "11434"  # Default port
            self.server_port_input.setText(server_port)
        
        new_url = f"http://{server_ip}:{server_port}/api/chat"
        
        # Update the worker with new URL
        if self.ollama_worker and self.ollama_thread and self.ollama_thread.isRunning():
            QMetaObject.invokeMethod(self.ollama_worker, "set_server_url", 
                                     Qt.QueuedConnection, Q_ARG(str, new_url))
            self.log_message(f"GUI: Updated Ollama server URL to {new_url}")
        else:
            self.log_message("GUI Warning: Ollama worker not available to update URL.")
            
    @Slot(str)
    def set_server_url(self, server_url):
        """Update the Ollama server URL"""
        try:
            global OLLAMA_API_URL
            OLLAMA_API_URL = server_url
            self.status_update.emit(f"Ollama: Server URL updated to {server_url}")
        except Exception as e:
            self.status_update.emit(f"Ollama Error: Failed to update server URL: {e}")

    @Slot()
    def on_apply_class_names(self):
        new_class_names = self.class_names_input.text().split(",")
        new_class_names = [name.strip() for name in new_class_names]
        if new_class_names != CLASS_NAMES:
            CLASS_NAMES.clear()
            CLASS_NAMES.extend(new_class_names)
            self.log_message(f"GUI: Class names updated to: {', '.join(CLASS_NAMES)}")
            self.class_names_input.setText(", ".join(CLASS_NAMES))
            
            # Update the worker's model if it's running and is a YOLOe model
            if (self.worker and self.perception_thread and self.perception_thread.isRunning() and 
                hasattr(self.worker, 'is_yoloe_model') and self.worker.is_yoloe_model):
                self.log_message("GUI: Updating YOLOe model with new class names...")
                # Instead of using QMetaObject.invokeMethod with Q_ARG(list), we'll use a direct call
                # which avoids the Qt type conversion issue
                self.worker.update_yoloe_classes(new_class_names)

    def setup_tts_worker(self):
        """Setup the TTS worker thread"""
        self.tts_thread = QThread(self)
        self.tts_worker = TTSWorker()
        self.tts_worker.moveToThread(self.tts_thread)

        # Connect signals
        self.tts_worker.status_update.connect(self.log_message)

        self.tts_thread.start()
        self.log_message("GUI: TTS worker thread started.")

    def _setup_robot_control_widget(self, right_layout):
        """Setup robot control widget after TTS worker is initialized"""
        if ROBOT_CONTROL_AVAILABLE:
            try:
                # Create object selector widget for robot navigation with TTS support
                self.object_selector_widget = ObjectSelectorWidget(self, tts_worker=self.tts_worker)
                right_layout.addWidget(self.object_selector_widget)

                # Set camera dimensions for image-based orientation
                if hasattr(self.object_selector_widget, 'husky_controller') and self.object_selector_widget.husky_controller:
                    # ZED camera default resolution (will be updated when camera starts)
                    self.object_selector_widget.husky_controller.set_camera_dimensions(1280, 720)

                self.log_message("GUI: Robot control integration successful")
            except Exception as e:
                self.log_message(f"GUI Error: Failed to integrate robot control: {e}")
                self.object_selector_widget = None

    def _setup_voice_intent_system(self):
        """Setup voice intent parser and executor"""
        try:
            self.voice_intent_parser = VoiceIntentParser()
            self.voice_intent_executor = VoiceIntentExecutor(self)

            # Set object selector reference when available
            if hasattr(self, 'object_selector_widget') and self.object_selector_widget:
                self.voice_intent_executor.set_object_selector(self.object_selector_widget)

            self.log_message("GUI: Voice intent system initialized")
        except Exception as e:
            self.log_message(f"GUI Error: Failed to initialize voice intent system: {e}")
            self.voice_intent_parser = None
            self.voice_intent_executor = None
        
    @Slot(int)
    def on_tts_enabled_changed(self, state):
        """Handle TTS checkbox state change"""
        enabled = state == Qt.CheckState.Checked.value
        self.log_message(f"GUI: TTS {'enabled' if enabled else 'disabled'}")
        if self.tts_worker:
            QMetaObject.invokeMethod(self.tts_worker, "set_enabled", Qt.QueuedConnection, Q_ARG(bool, enabled))
            
# --- Ollama Worker (moved to ai/ollama_worker.py) ---






# --- TTS Worker ---
class TTSWorker(QObject):
    status_update = Signal(str)
    finished = Signal()

    def __init__(self):
        super().__init__()
        self._running = False
        self._lock = threading.Lock()
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.tts = None
        self.enabled = False
        
    def _is_running(self):
        with self._lock:
            return self._running

    @Slot(str)
    def speak_scene_analysis_text(self, text):
        """Convert scene analysis text to speech and save it"""
        if not self.enabled or self.tts is None:
            return

        try:
            # Process the text before speaking
            processed_text = self._process_text_for_speech(text)

            # If there's nothing to speak after processing, return
            if not processed_text:
                return

            # For scene analysis, always save the audio file
            import re
            request_id_match = re.search(r'\[Request ID: ([0-9_]+)\]', text)
            request_id = request_id_match.group(1) if request_id_match else generate_request_id()

            # Create a short slug from the text (first 20 chars)
            text_slug = processed_text[:20].replace(" ", "_").replace("/", "_").replace("\\", "_")
            text_slug = ''.join(c for c in text_slug if c.isalnum() or c == '_')
            filename = f"scene_analysis_{text_slug}_{request_id}.wav"
            filepath = os.path.join("tts_output", filename)

            # Ensure output directory exists
            os.makedirs("tts_output", exist_ok=True)

            # Generate speech and save
            self.tts.tts_to_file(text=processed_text, file_path=filepath)
            self.status_update.emit(f"TTS: Scene analysis audio saved to {filepath}")

            # Play the audio file
            import pygame
            pygame.mixer.init()
            pygame.mixer.music.load(filepath)
            pygame.mixer.music.play()

            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                pygame.time.Clock().tick(10)

            # Clean up
            pygame.mixer.quit()

        except Exception as e:
            self.status_update.emit(f"TTS Scene Analysis Error: Failed to speak text: {e}")
            
    def initialize_tts(self):
        """Initialize the TTS model"""
        try:
            self.tts = TTS("tts_models/de/thorsten/vits").to(self.device)                   # tts_models/de/css10/vits-neon #tts_models/de/thorsten/vits
            self.status_update.emit(f"TTS: Model initialized on {self.device}")

            # Generate navigation audio files if they don't exist
            self._generate_navigation_audio_files()

            return True
        except Exception as e:
            self.status_update.emit(f"TTS Error: Failed to initialize model: {e}")
            return False

    def _generate_navigation_audio_files(self):
        """Generate static navigation audio files if they don't exist"""
        try:
            # Ensure tts_output directory exists
            os.makedirs("tts_output", exist_ok=True)

            # Define navigation messages and their corresponding filenames
            navigation_messages = {
                "Ziel erreicht!": "nav_ziel_erreicht.wav",
                "Ziel nicht gefunden!": "nav_ziel_nicht_gefunden.wav",
                "Navigation gestoppt!": "nav_navigation_gestoppt.wav"
            }

            for message, filename in navigation_messages.items():
                filepath = os.path.join("tts_output", filename)

                # Only generate if file doesn't exist
                if not os.path.exists(filepath):
                    self.status_update.emit(f"TTS: Generating navigation audio: {message}")
                    self.tts.tts_to_file(text=message, file_path=filepath)
                    self.status_update.emit(f"TTS: Saved navigation audio to {filepath}")
                else:
                    self.status_update.emit(f"TTS: Navigation audio already exists: {filename}")

        except Exception as e:
            self.status_update.emit(f"TTS Error: Failed to generate navigation audio files: {e}")
            
    @Slot(bool)
    def set_enabled(self, enabled):
        """Enable/disable TTS"""
        self.enabled = enabled
        if enabled and self.tts is None:
            self.initialize_tts()
            
    @Slot(str)
    def speak_navigation_text(self, text):
        """Convert navigation text to speech and play it"""
        if not self.enabled or self.tts is None:
            return

        try:
            # Process the text before speaking
            processed_text = self._process_text_for_speech(text)

            # If there's nothing to speak after processing, return
            if not processed_text:
                return

            # Check if this is a static navigation message that has a pre-generated file
            static_navigation_files = {
                "Ziel erreicht!": "nav_ziel_erreicht.wav",
                "Ziel nicht gefunden!": "nav_ziel_nicht_gefunden.wav",
                "Navigation gestoppt!": "nav_navigation_gestoppt.wav"
            }

            # Note: "Folge [object] [id]" messages are dynamic and will use temporary files

            if processed_text in static_navigation_files:
                # Use pre-generated file
                filename = static_navigation_files[processed_text]
                filepath = os.path.join("tts_output", filename)

                if os.path.exists(filepath):
                    # Play the pre-generated file
                    self._play_audio_file_internal(filepath)
                    return
                else:
                    self.status_update.emit(f"TTS Warning: Pre-generated navigation file not found: {filename}")
                    # Fall through to generate temporary file

            # For dynamic messages (like "Navigiere zu person 0"), create temporary file
            import tempfile

            # Create temporary file with .wav extension
            temp_fd, temp_filepath = tempfile.mkstemp(suffix='.wav', prefix='nav_tts_')
            os.close(temp_fd)  # Close the file descriptor, we only need the path

            # Generate speech to temporary file
            self.tts.tts_to_file(text=processed_text, file_path=temp_filepath)

            # Play the audio file
            self._play_audio_file_internal(temp_filepath)

            # Delete temporary file
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)

        except Exception as e:
            self.status_update.emit(f"TTS Navigation Error: Failed to speak text: {e}")

    def _play_audio_file_internal(self, filepath):
        """Internal method to play an audio file"""
        import pygame
        pygame.mixer.init()
        pygame.mixer.music.load(filepath)
        pygame.mixer.music.play()

        # Wait for playback to finish
        while pygame.mixer.music.get_busy():
            pygame.time.Clock().tick(10)

        # Clean up
        pygame.mixer.quit()

    @Slot(str)
    def speak_text(self, text):
        """Convert text to speech and play it"""
        if not self.enabled or self.tts is None:
            return

        try:
            # Process the text before speaking
            processed_text = self._process_text_for_speech(text)

            # If there's nothing to speak after processing, return
            if not processed_text:
                return

            # For normal chat responses, always use temporary files (no saving)
            filename = f"tts_temp_{generate_request_id()}.wav"
            filepath = os.path.join("tts_output", filename)
            should_save = False
            
            # Ensure output directory exists
            os.makedirs("tts_output", exist_ok=True)
            
            # Check if we should generate the speech or use existing file
            if not os.path.exists(filepath) or not should_save:
                # Generate speech
                self.tts.tts_to_file(text=processed_text, file_path=filepath)
                if should_save:
                    self.status_update.emit(f"TTS: Saved audio to {filepath}")
            
            # Play the audio file
            import pygame
            pygame.mixer.init()
            pygame.mixer.music.load(filepath)
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                pygame.time.Clock().tick(10)
                
            # Clean up
            pygame.mixer.quit()
            
            # Delete temporary files (errors or when saving is disabled)
            if not should_save and os.path.exists(filepath):
                os.remove(filepath)
            
        except Exception as e:
            self.status_update.emit(f"TTS Error: Failed to speak text: {e}")
    
    def _process_text_for_speech(self, text):
        """Process text to remove request IDs and handle error messages, and convert units for better TTS"""
        # Check if text contains error messages
        if "Error:" in text or "error" in text.lower():
            return "Es gab einen Fehler beim Senden an das Modell."
            
        # Remove request ID pattern [Request ID: YYYYMMDD_HHMMSS_XXXX]
        import re
        text = re.sub(r'\[Request ID: [0-9_]+\]', '', text)
        
        # Remove any remaining square brackets content that might be metadata
        text = re.sub(r'\[[^\]]*\]', '', text)
        
        # Replace decimal numbers with comma to explicit "komma" for better TTS pronunciation
        # Match patterns like "0,8" and replace with "0 komma 8"
        text = re.sub(r'(\d+),(\d+)', r'\1 komma \2', text)
        
        # Replace abbreviated units with full words for better TTS pronunciation
        # Match numbers followed by units, preserving the number and replacing the unit
        # Handle distances: cm, m
        text = re.sub(r'(\d+(?:,\d+)?)\s*cm\b', r'\1 Zentimeter', text)
        text = re.sub(r'(\d+(?:,\d+)?)\s*m\b(?!\s*\/)', r'\1 Meter', text)
        
        # Handle speeds: m/s, km/h
        text = re.sub(r'(\d+(?:,\d+)?)\s*m\/s\b', r'\1 Meter pro Sekunde', text)
        text = re.sub(r'(\d+(?:,\d+)?)\s*km\/h\b', r'\1 Kilometer pro Stunde', text)
        
        # Handle parentheses with measurements like "(ca. 40cm)"
        text = re.sub(r'\(ca\.\s*(\d+(?:,\d+)?)\s*cm\)', r'(etwa \1 Zentimeter)', text)
        text = re.sub(r'\(ca\.\s*(\d+(?:,\d+)?)\s*m\)', r'(etwa \1 Meter)', text)
        
        # Handle other common abbreviations
        text = re.sub(r'\bca\.\s*', r'etwa ', text)
        
        # Handle grammatical corrections for the number "1" with units
        text = re.sub(r'\b1\s+Meter\b', r'ein Meter', text)
        text = re.sub(r'\b1\s+Zentimeter\b', r'ein Zentimeter', text)
        text = re.sub(r'\b1\s+Kilometer\b', r'ein Kilometer', text)
        
        # Remove markdown formatting with asterisks (both bold and italic)
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold: **text** -> text
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # Italic: *text* -> text
        
        # Trim whitespace and return
        return text.strip()
    
    @Slot(str)
    def play_audio_file(self, filepath):
        """Play an existing audio file"""
        if not self.enabled:
            return
            
        try:
            # Play the audio file
            import pygame
            pygame.mixer.init()
            pygame.mixer.music.load(filepath)
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                pygame.time.Clock().tick(10)
                
            # Clean up
            pygame.mixer.quit()
            
        except Exception as e:
            self.status_update.emit(f"TTS Error: Failed to play audio file: {e}")
            
    @Slot()
    def stop(self):
        """Stop the TTS worker"""
        with self._lock:
            self._running = False
        self.finished.emit()

# --- Main Application ---
def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
