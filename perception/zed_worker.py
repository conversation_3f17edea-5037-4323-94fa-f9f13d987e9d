"""
ZED Camera and YOLO Object Detection Worker for pAIrSEEption

This module handles ZED camera capture, YOLO object detection, and 3D position tracking.
"""

import os
import threading
import time
import copy
import csv
import cv2
import numpy as np
import pyzed.sl as sl
from PySide6.QtCore import QObject, Signal, Slot

# Import configuration constants
from core import (
    AVAILABLE_YOLO_MODELS, DEFAULT_YOLO_MODEL, YOLO_MODEL_PATH,
    ZED_RESOLUTION, ZED_FPS, ZED_DEPTH_MODE, ZED_COORDINATE_SYSTEM,
    INITIAL_CONFIDENCE_THRESHOLD,
    OBJECT_LOG_CSV, DISTANCE_LOG_CSV, PERFORMANCE_LOG_CSV,
    ZED_DETECTION_CONFIDENCE_THRESHOLD, ZED_ENABLE_TRACKING, ZED_ENABLE_SEGMENTATION
)

# YOLO imports
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    torch = None 
    TORCH_AVAILABLE = False

from ultralytics import YOL<PERSON>, Y<PERSON><PERSON>
from ultralytics.utils.plotting import Annotator 

# CSV headers for logging
OBJECT_LOG_HEADER = ['timestamp', 'frame_id', 'object_id', 'class_name', 'confidence', 'x', 'y', 'z', 'velocity_x', 'velocity_y', 'velocity_z']
DISTANCE_LOG_HEADER = ['timestamp', 'frame_id', 'object_id', 'class_name', 'distance', 'angle']
PERFORMANCE_LOG_HEADER = ['timestamp', 'frame_id', 'fps', 'processing_time_ms', 'yolo_time_ms', 'zed_time_ms']

def initialize_csv(filepath, header):
    """Initialize CSV file with header if it doesn't exist"""
    write_header = not os.path.exists(filepath)
    try:
        with open(filepath, 'a', newline='') as csvfile:
            writer = csv.writer(csvfile)
            if write_header:
                writer.writerow(header)
    except IOError as e:
        print(f"Error accessing CSV {filepath}: {e}")


class PerceptionWorker(QObject):
    """
    Worker class for ZED camera capture and YOLO object detection.
    Handles 3D position tracking and object state management.
    """
    frame_ready = Signal(np.ndarray)
    scene_data_ready = Signal(list, np.ndarray, list)
    status_update = Signal(str)
    finished = Signal()
    model_changed = Signal(str)  # Signal to notify when model changes
    stats_update = Signal(dict)  # Signal for statistics updates

    def __init__(self):
        super().__init__()
        self._running = False
        self._lock = threading.Lock()
        self._current_conf_threshold = INITIAL_CONFIDENCE_THRESHOLD
        self._threshold_lock = threading.Lock()
        self._current_model_path = YOLO_MODEL_PATH
        self._model_path_lock = threading.Lock()
        self._model_reload_requested = False
        self._model_reload_lock = threading.Lock()
        
        # ZED SDK objects
        self.zed = None
        self.yolo_model = None
        self.init_params = None
        self.runtime_params = None
        self.obj_det_params = None
        self.obj_det_runtime_params = None
        self.left_image = sl.Mat()
        self.objects = sl.Objects()
        
        # Object tracking
        self.latest_object_states = []
        self.latest_states_lock = threading.Lock()
        self.current_frame_sl_masks = []
        
        # Display options
        self._display_options_lock = threading.Lock()
        self._show_yolo_plot_bboxes_labels_conf = True
        self._show_yolo_plot_masks = True
        self._show_zed_id = True
        self._show_zed_pos = True
        self._show_zed_speed = False

    def _is_running(self):
        """Thread-safe check if worker is running"""
        with self._lock:
            return self._running

    @Slot(float)
    def set_confidence_threshold(self, threshold):
        """Set the confidence threshold for object detection"""
        with self._threshold_lock:
            self._current_conf_threshold = threshold
        self.status_update.emit(f"Worker: Confidence threshold set to {threshold}")

    @Slot(str)
    def set_model_path(self, model_path):
        """Set the YOLO model path and request reload"""
        with self._model_path_lock:
            self._current_model_path = model_path
        with self._model_reload_lock:
            self._model_reload_requested = True
        self.status_update.emit(f"Worker: Model change requested to {model_path}")

    @Slot(bool)
    def set_show_yolo_plot_bboxes_labels_conf(self, show):
        """Set whether to show YOLO bounding boxes, labels, and confidence"""
        with self._display_options_lock:
            self._show_yolo_plot_bboxes_labels_conf = show

    @Slot(bool)
    def set_show_yolo_plot_masks(self, show):
        """Set whether to show YOLO segmentation masks"""
        with self._display_options_lock:
            self._show_yolo_plot_masks = show

    @Slot(bool)
    def set_show_zed_id(self, show):
        """Set whether to show ZED object IDs"""
        with self._display_options_lock:
            self._show_zed_id = show

    @Slot(bool)
    def set_show_zed_pos(self, show):
        """Set whether to show ZED object positions"""
        with self._display_options_lock:
            self._show_zed_pos = show

    @Slot(bool)
    def set_show_zed_speed(self, show):
        """Set whether to show ZED object speeds"""
        with self._display_options_lock:
            self._show_zed_speed = show

    def initialize_resources(self):
        """Initialize ZED camera and YOLO model"""
        try:
            self.status_update.emit("Worker: Initializing resources...")
            
            # Load YOLO model
            if not self._load_yolo_model():
                return False
            
            # Initialize ZED camera
            if not self._initialize_zed_camera():
                return False
            
            # Initialize CSV files
            self._initialize_csv_files()
            
            self.status_update.emit("Worker: Initialization complete.")
            return True
            
        except Exception as e:
            self.status_update.emit(f"Worker: Initialization FAILED: {e}")
            self.cleanup_resources()
            return False

    def _load_yolo_model(self):
        """Load the YOLO model"""
        try:
            with self._model_path_lock:
                model_path = self._current_model_path
            
            if not model_path:
                self.status_update.emit("Worker: No YOLO model path specified")
                return False
            
            self.status_update.emit(f"Worker: Loading YOLO model from {model_path}...")
            
            # Determine model type and load accordingly
            if "yoloe" in model_path.lower():
                self.yolo_model = YOLOE(model_path)
            else:
                self.yolo_model = YOLO(model_path)
            
            # Set device preference
            if TORCH_AVAILABLE and torch.cuda.is_available():
                self.yolo_model.to('cuda')
                self.status_update.emit("Worker: YOLO model loaded on GPU")
            else:
                self.status_update.emit("Worker: YOLO model loaded on CPU")
            
            return True
            
        except Exception as e:
            self.status_update.emit(f"Worker: Failed to load YOLO model: {e}")
            return False

    def _initialize_zed_camera(self):
        """Initialize ZED camera and object detection"""
        try:
            self.status_update.emit("Worker: Initializing ZED camera object...")
            self.zed = sl.Camera()
            
            # Set initialization parameters
            self.init_params = sl.InitParameters(
                camera_resolution=ZED_RESOLUTION, 
                camera_fps=ZED_FPS, 
                depth_mode=ZED_DEPTH_MODE,
                coordinate_units=sl.UNIT.METER, 
                coordinate_system=ZED_COORDINATE_SYSTEM,
                depth_minimum_distance=0.4, 
                depth_maximum_distance=20.0, 
                enable_image_enhancement=True
            )
            
            # Open camera
            self.status_update.emit("Worker: Opening ZED camera...")
            err = self.zed.open(self.init_params)
            if err != sl.ERROR_CODE.SUCCESS:
                raise Exception(f"ZED Open Error: {err}")
            
            # Validate camera calibration
            self._validate_camera_calibration()
            
            # Enable positional tracking
            self._enable_positional_tracking()
            
            # Enable object detection
            self._enable_object_detection()
            
            # Set runtime parameters
            self.runtime_params = sl.RuntimeParameters()
            self.runtime_params.confidence_threshold = 50
            self.runtime_params.texture_confidence_threshold = 100
            
            self.status_update.emit("Worker: ZED camera initialized successfully")
            return True
            
        except Exception as e:
            self.status_update.emit(f"Worker: ZED camera initialization failed: {e}")
            return False

    def _validate_camera_calibration(self):
        """Validate ZED camera calibration parameters"""
        cam_info = self.zed.get_camera_information()
        calib_params = cam_info.camera_configuration.calibration_parameters
        
        if calib_params is None or calib_params.left_cam is None:
            self.status_update.emit("[ZED WARNING] Calibration parameters structure missing!")
        elif calib_params.left_cam.fx == 0:
            self.status_update.emit("[ZED WARNING] Calibration fx=0 suggests failed self-calibration or missing factory calibration.")
        else:
            self.status_update.emit("Worker: Camera calibration parameters seem valid (fx != 0).")

    def _enable_positional_tracking(self):
        """Enable ZED positional tracking"""
        self.status_update.emit("Worker: Enabling Positional Tracking...")
        pos_params = sl.PositionalTrackingParameters()
        pos_params.set_as_static = True 
        pos_params.enable_area_memory = True 
        err = self.zed.enable_positional_tracking(pos_params)
        if err != sl.ERROR_CODE.SUCCESS:
            raise Exception(f"Pos Tracking Error: {err}")
        self.status_update.emit("Worker: Positional Tracking enabled (static, area_memory).")

    def _enable_object_detection(self):
        """Enable ZED object detection with custom parameters"""
        self.status_update.emit("Worker: Enabling Object Detection...")
        
        # Configure object detection parameters
        self.obj_det_params = sl.ObjectDetectionParameters()
        self.obj_det_params.detection_model = sl.OBJECT_DETECTION_MODEL.CUSTOM_BOX_OBJECTS
        self.obj_det_params.enable_tracking = ZED_ENABLE_TRACKING
        self.obj_det_params.enable_segmentation = ZED_ENABLE_SEGMENTATION
        
        # Enable object detection
        err = self.zed.enable_object_detection(self.obj_det_params)
        if err != sl.ERROR_CODE.SUCCESS:
            raise Exception(f"Object Detection Error: {err}")
        
        # Set runtime parameters
        self.obj_det_runtime_params = sl.ObjectDetectionRuntimeParameters()
        self.obj_det_runtime_params.detection_confidence_threshold = ZED_DETECTION_CONFIDENCE_THRESHOLD
        
        self.status_update.emit(f"Worker: Object Detection enabled (tracking: {ZED_ENABLE_TRACKING}, segmentation: {ZED_ENABLE_SEGMENTATION})")

    def _initialize_csv_files(self):
        """Initialize CSV files for logging"""
        self.status_update.emit("Worker: Initializing CSV files...")
        initialize_csv(OBJECT_LOG_CSV, OBJECT_LOG_HEADER)
        initialize_csv(DISTANCE_LOG_CSV, DISTANCE_LOG_HEADER)
        initialize_csv(PERFORMANCE_LOG_CSV, PERFORMANCE_LOG_HEADER)

    @Slot()
    def run(self):
        """Main processing loop for ZED camera and YOLO detection"""
        with self._lock:
            self._running = True

        self.status_update.emit("Worker: Starting main processing loop...")

        if not self.initialize_resources():
            self.status_update.emit("Worker: Failed to initialize resources")
            self.finished.emit()
            return

        frame_count = 0
        fps_counter = 0
        fps_start_time = time.time()

        try:
            while self._is_running():
                loop_start_time = time.time()

                # Check for model reload request
                self._check_model_reload()

                # Capture frame from ZED
                if not self._capture_zed_frame():
                    continue

                # Process YOLO detection
                yolo_start_time = time.time()
                yolo_results = self._process_yolo_detection()
                yolo_time = (time.time() - yolo_start_time) * 1000

                if yolo_results is None:
                    continue

                # Process ZED object detection
                zed_start_time = time.time()
                zed_objects = self._process_zed_detection(yolo_results)
                zed_time = (time.time() - zed_start_time) * 1000

                # Create annotated image
                annotated_image = self._create_annotated_image(yolo_results, zed_objects)

                # Update object states
                self._update_object_states(zed_objects)

                # Log data
                self._log_frame_data(frame_count, zed_objects, yolo_time, zed_time)

                # Emit signals
                self.frame_ready.emit(annotated_image)
                self.scene_data_ready.emit(zed_objects, annotated_image, self.latest_object_states)

                # Calculate FPS
                fps_counter += 1
                if fps_counter >= 30:  # Update FPS every 30 frames
                    fps_elapsed = time.time() - fps_start_time
                    current_fps = fps_counter / fps_elapsed if fps_elapsed > 0 else 0

                    stats = {
                        'fps': current_fps,
                        'frame_count': frame_count,
                        'yolo_time_ms': yolo_time,
                        'zed_time_ms': zed_time,
                        'total_objects': len(zed_objects)
                    }
                    self.stats_update.emit(stats)

                    fps_counter = 0
                    fps_start_time = time.time()

                frame_count += 1

                # Small delay to prevent overwhelming the system
                time.sleep(0.001)

        except Exception as e:
            self.status_update.emit(f"Worker: Error in main loop: {e}")
        finally:
            self.cleanup_resources()
            self.status_update.emit("Worker: Main loop finished")
            self.finished.emit()

    def _check_model_reload(self):
        """Check if model reload is requested and perform it"""
        with self._model_reload_lock:
            if self._model_reload_requested:
                self._model_reload_requested = False
                self.status_update.emit("Worker: Reloading YOLO model...")
                if self._load_yolo_model():
                    with self._model_path_lock:
                        model_path = self._current_model_path
                    self.model_changed.emit(model_path)
                    self.status_update.emit("Worker: Model reloaded successfully")
                else:
                    self.status_update.emit("Worker: Model reload failed")

    def _capture_zed_frame(self):
        """Capture frame from ZED camera"""
        try:
            err = self.zed.grab(self.runtime_params)
            if err == sl.ERROR_CODE.SUCCESS:
                self.zed.retrieve_image(self.left_image, sl.VIEW.LEFT)
                return True
            else:
                if err != sl.ERROR_CODE.END_OF_SVOFILE_REACHED:
                    self.status_update.emit(f"Worker: ZED grab error: {err}")
                return False
        except Exception as e:
            self.status_update.emit(f"Worker: Error capturing ZED frame: {e}")
            return False

    def _process_yolo_detection(self):
        """Process YOLO object detection on current frame"""
        try:
            # Convert ZED image to OpenCV format
            image_cv = self.left_image.get_data()
            image_rgb = cv2.cvtColor(image_cv, cv2.COLOR_BGRA2RGB)

            # Get current confidence threshold
            with self._threshold_lock:
                conf_threshold = self._current_conf_threshold

            # Run YOLO inference
            results = self.yolo_model(image_rgb, conf=conf_threshold, verbose=False)

            return results[0] if results else None

        except Exception as e:
            self.status_update.emit(f"Worker: YOLO detection error: {e}")
            return None

    def _process_zed_detection(self, yolo_results):
        """Process ZED object detection using YOLO results"""
        try:
            # Convert YOLO results to ZED custom objects
            custom_objects = self._convert_yolo_to_zed_objects(yolo_results)

            if not custom_objects:
                return []

            # Ingest custom objects into ZED
            self.zed.ingest_custom_box_objects(custom_objects)

            # Retrieve ZED objects with 3D information
            err = self.zed.retrieve_objects(self.objects, self.obj_det_runtime_params)
            if err != sl.ERROR_CODE.SUCCESS:
                return []

            # Convert ZED objects to our format
            return self._convert_zed_objects_to_dict()

        except Exception as e:
            self.status_update.emit(f"Worker: ZED detection error: {e}")
            return []

    def _convert_yolo_to_zed_objects(self, yolo_results):
        """Convert YOLO detection results to ZED custom objects"""
        try:
            custom_objects = []

            if yolo_results.boxes is None:
                return custom_objects

            boxes = yolo_results.boxes.xyxy.cpu().numpy()
            confidences = yolo_results.boxes.conf.cpu().numpy()
            class_ids = yolo_results.boxes.cls.cpu().numpy().astype(int)

            for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
                # Create ZED custom object
                custom_obj = sl.CustomBoxObjectData()

                # Set bounding box (x1, y1, x2, y2)
                custom_obj.bounding_box_2d = sl.Rect(
                    int(box[0]), int(box[1]),
                    int(box[2] - box[0]), int(box[3] - box[1])
                )

                # Set label and confidence
                custom_obj.label = class_id
                custom_obj.probability = conf

                # Set unique ID for tracking
                custom_obj.unique_object_id = sl.generate_unique_id()

                # Add segmentation mask if available
                if hasattr(yolo_results, 'masks') and yolo_results.masks is not None:
                    try:
                        mask_data = yolo_results.masks.data[i].cpu().numpy()
                        if mask_data.shape[0] > 0:
                            # Resize mask to image dimensions
                            image_height, image_width = self.left_image.get_height(), self.left_image.get_width()
                            mask_resized = cv2.resize(mask_data, (image_width, image_height))

                            # Convert to binary mask
                            mask_binary = (mask_resized > 0.5).astype(np.uint8)

                            # Create ZED mask
                            zed_mask = sl.Mat()
                            zed_mask.init_mat(image_width, image_height, sl.MAT_TYPE.U8_C1)
                            zed_mask.set_from_numpy(mask_binary)
                            custom_obj.mask = zed_mask
                    except Exception as mask_error:
                        # Continue without mask if there's an error
                        pass

                custom_objects.append(custom_obj)

            return custom_objects

        except Exception as e:
            self.status_update.emit(f"Worker: Error converting YOLO to ZED objects: {e}")
            return []

    def _convert_zed_objects_to_dict(self):
        """Convert ZED objects to dictionary format"""
        try:
            object_list = []

            for obj in self.objects.object_list:
                # Get class name from YOLO model
                class_name = "Unknown"
                if hasattr(self.yolo_model, 'names') and obj.label < len(self.yolo_model.names):
                    class_name = self.yolo_model.names[obj.label]

                # Get 3D position
                position = obj.position
                position_list = [position[0], position[1], position[2]] if position else [0, 0, 0]

                # Get velocity
                velocity = obj.velocity
                velocity_list = [velocity[0], velocity[1], velocity[2]] if velocity else [0, 0, 0]

                # Create object dictionary
                obj_dict = {
                    'object_id': obj.id,
                    'class_name': class_name,
                    'label_id': obj.label,
                    'confidence': obj.confidence / 100.0,  # Convert from 0-100 to 0-1
                    'position': position_list,
                    'velocity': velocity_list,
                    'bounding_box_2d': {
                        'x': obj.bounding_box_2d.x,
                        'y': obj.bounding_box_2d.y,
                        'width': obj.bounding_box_2d.width,
                        'height': obj.bounding_box_2d.height
                    },
                    'tracking_state': str(obj.tracking_state),
                    'action_state': str(obj.action_state)
                }

                object_list.append(obj_dict)

            return object_list

        except Exception as e:
            self.status_update.emit(f"Worker: Error converting ZED objects: {e}")
            return []

    def _create_annotated_image(self, yolo_results, zed_objects):
        """Create annotated image with YOLO and ZED information"""
        try:
            # Get base image
            image_cv = self.left_image.get_data()
            image_rgb = cv2.cvtColor(image_cv, cv2.COLOR_BGRA2RGB)
            annotated_image = image_rgb.copy()

            # Get display options
            with self._display_options_lock:
                show_yolo_bbox = self._show_yolo_plot_bboxes_labels_conf
                show_yolo_masks = self._show_yolo_plot_masks
                show_zed_id = self._show_zed_id
                show_zed_pos = self._show_zed_pos
                show_zed_speed = self._show_zed_speed

            # Draw YOLO annotations
            if show_yolo_bbox and yolo_results.boxes is not None:
                annotator = Annotator(annotated_image)

                boxes = yolo_results.boxes.xyxy.cpu().numpy()
                confidences = yolo_results.boxes.conf.cpu().numpy()
                class_ids = yolo_results.boxes.cls.cpu().numpy().astype(int)

                for box, conf, class_id in zip(boxes, confidences, class_ids):
                    # Get class name
                    class_name = "Unknown"
                    if hasattr(self.yolo_model, 'names') and class_id < len(self.yolo_model.names):
                        class_name = self.yolo_model.names[class_id]

                    # Draw bounding box
                    label = f"{class_name} {conf:.2f}"
                    annotator.box_label(box, label)

                annotated_image = annotator.result()

            # Draw YOLO masks
            if show_yolo_masks and hasattr(yolo_results, 'masks') and yolo_results.masks is not None:
                try:
                    masks = yolo_results.masks.data.cpu().numpy()
                    for mask in masks:
                        if mask.shape[0] > 0:
                            # Resize mask to image dimensions
                            mask_resized = cv2.resize(mask, (annotated_image.shape[1], annotated_image.shape[0]))
                            mask_binary = (mask_resized > 0.5).astype(np.uint8)

                            # Create colored overlay
                            color = np.random.randint(0, 255, 3)
                            colored_mask = np.zeros_like(annotated_image)
                            colored_mask[mask_binary == 1] = color

                            # Blend with image
                            annotated_image = cv2.addWeighted(annotated_image, 0.8, colored_mask, 0.2, 0)
                except Exception as mask_error:
                    pass  # Continue without masks if there's an error

            # Draw ZED annotations
            for obj in zed_objects:
                bbox = obj['bounding_box_2d']
                x, y, w, h = bbox['x'], bbox['y'], bbox['width'], bbox['height']

                # Draw ZED ID
                if show_zed_id:
                    cv2.putText(annotated_image, f"ID:{obj['object_id']}",
                               (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                # Draw position
                if show_zed_pos:
                    pos = obj['position']
                    pos_text = f"({pos[0]:.1f}, {pos[1]:.1f}, {pos[2]:.1f})"
                    cv2.putText(annotated_image, pos_text,
                               (x, y + h + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)

                # Draw speed
                if show_zed_speed:
                    vel = obj['velocity']
                    speed = np.sqrt(vel[0]**2 + vel[1]**2 + vel[2]**2)
                    speed_text = f"Speed: {speed:.1f}m/s"
                    cv2.putText(annotated_image, speed_text,
                               (x, y + h + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

            return annotated_image

        except Exception as e:
            self.status_update.emit(f"Worker: Error creating annotated image: {e}")
            # Return original image if annotation fails
            image_cv = self.left_image.get_data()
            return cv2.cvtColor(image_cv, cv2.COLOR_BGRA2RGB)

    def _update_object_states(self, zed_objects):
        """Update the latest object states for external access"""
        with self.latest_states_lock:
            self.latest_object_states = copy.deepcopy(zed_objects)

    def _log_frame_data(self, frame_count, zed_objects, yolo_time, zed_time):
        """Log frame data to CSV files"""
        try:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

            # Log object data
            for obj in zed_objects:
                pos = obj['position']
                vel = obj['velocity']

                object_row = [
                    timestamp, frame_count, obj['object_id'], obj['class_name'],
                    obj['confidence'], pos[0], pos[1], pos[2],
                    vel[0], vel[1], vel[2]
                ]

                with open(OBJECT_LOG_CSV, 'a', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(object_row)

                # Log distance data
                distance = np.sqrt(pos[0]**2 + pos[1]**2 + pos[2]**2)
                angle = np.arctan2(pos[1], pos[0]) * 180 / np.pi

                distance_row = [
                    timestamp, frame_count, obj['object_id'], obj['class_name'],
                    distance, angle
                ]

                with open(DISTANCE_LOG_CSV, 'a', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(distance_row)

            # Log performance data (every 10 frames to reduce overhead)
            if frame_count % 10 == 0:
                total_time = yolo_time + zed_time
                fps = 1000 / total_time if total_time > 0 else 0

                performance_row = [
                    timestamp, frame_count, fps, total_time, yolo_time, zed_time
                ]

                with open(PERFORMANCE_LOG_CSV, 'a', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(performance_row)

        except Exception as e:
            # Don't emit status updates for logging errors to avoid spam
            pass

    def get_latest_object_states(self):
        """Get the latest object states (thread-safe)"""
        with self.latest_states_lock:
            return copy.deepcopy(self.latest_object_states)

    def cleanup_resources(self):
        """Clean up ZED camera and other resources"""
        try:
            if self.zed is not None:
                self.status_update.emit("Worker: Cleaning up ZED resources...")
                self.zed.disable_object_detection()
                self.zed.disable_positional_tracking()
                self.zed.close()
                self.zed = None
                self.status_update.emit("Worker: ZED resources cleaned up")
        except Exception as e:
            self.status_update.emit(f"Worker: Error during cleanup: {e}")

    @Slot(list)
    def update_yoloe_classes(self, class_names):
        """Update YOLOe model with new class names"""
        if not self.is_yoloe_model:
            self.status_update.emit("Worker: Cannot update classes - current model is not a YOLOe model")
            return

        self._apply_yoloe_classes(self.yolo_model, class_names)

    def _apply_yoloe_classes(self, model, class_names):
        """Apply class names to YOLOe model"""
        self.status_update.emit("Worker: Applying YOLOE set_classes...")
        try:
            if hasattr(model, 'set_classes'):
                model.set_classes(class_names)
                self.status_update.emit(f"Worker: YOLOE classes updated: {class_names}")
            else:
                self.status_update.emit("Worker Warning: Model does not support set_classes method")
        except Exception as e:
            self.status_update.emit(f"Worker Warning: YOLOE set_classes failed: {e}")

    @Slot()
    def stop(self):
        """Stop the worker"""
        with self._lock:
            self._running = False
        self.status_update.emit("Worker: Stop requested...")
        # Note: cleanup_resources() will be called in the run() method's finally block
