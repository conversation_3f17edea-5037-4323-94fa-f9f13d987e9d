# Core dependencies for the pAIrSEEption application

# Computer Vision and Image Processing
opencv-python>=4.8.0
numpy>=1.26.0      # tested on Jetson with numpy version 1.26.4 and ZED SDK 5.0 EA
pillow>=9.0.0

# Deep Learning and YOLO Models
#torch>=2.0.0 # install separately based on system (CPU/GPU)
ultralytics>=8.0.0

# ZED SDK (requires separate installation from Stereolabs)
# pyzed  # Install separately from https://www.stereolabs.com/developers/release/

# GUI Framework
PySide6>=6.5.0

# Audio Processing and Speech Recognition
openai-whisper>=20231117
sounddevice>=0.4.6
soundfile>=0.12.1  # For saving audio files (optional, for debugging)

# Text-to-Speech
coqui-tts        # Coqui TTS library
pygame>=2.5.0      # For audio playback

# API Clients
openai>=1.0.0       # For OpenRouter API
requests>=2.31.0    # For Ollama API requests

# System Monitoring
psutil>=5.9.0
nvidia-ml-py3>=7.352.0  # For GPU monitoring (NVIDIA/Jetson)

# Standard library packages (usually included with Python)
# sys, csv, json, math, os, threading, time, copy, base64, datetime, io
# These are part of Python's standard library and don't need to be installed

# Optional: Additional audio backends (if needed)
# pyaudio>=0.2.11  # Alternative audio backend
# librosa>=0.10.0  # Advanced audio processing

# Development and debugging (optional)
# jupyter>=1.0.0
# matplotlib>=3.5.0
# pandas>=1.5.0
