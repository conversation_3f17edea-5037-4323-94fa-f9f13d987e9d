# Core dependencies for the pAIrSEEption application
# Last updated: 2025-07-07

# -----------------------------------------------------------------------------
# CORE LIBRARIES
# -----------------------------------------------------------------------------

# Computer Vision and Image Processing
opencv-python>=4.8.0
numpy>=1.26.4
pillow>=9.0.0

# Deep Learning and Object Detection
ultralytics>=8.0.0

# GUI Framework
PySide6>=6.5.0

# API Clients
openai>=1.0.0       # For OpenRouter API
requests>=2.31.0    # For Ollama API requests
ollama>=0.2.0       # Official Ollama Python client

# System Monitoring
psutil>=5.9.0
nvidia-ml-py3     # For NVIDIA GPU monitoring (pynvml)

# Configuration File Parsing
PyYAML>=6.0


# -----------------------------------------------------------------------------
# SPEECH AND AUDIO
# -----------------------------------------------------------------------------

# Speech-to-Text (STT)
faster-whisper>=1.0.0 # High-performance Whisper implementation

# Text-to-Speech (TTS)
coqui-tts>=0.26.0       # Coqui TTS library
pygame>=2.5.0       # Used by TTS for audio playback

# Audio I/O
sounddevice>=0.4.6
soundfile>=0.12.1   # For saving audio files (optional, for debugging)

# -----------------------------------------------------------------------------
# HARDWARE & PLATFORM SPECIFIC (MANUAL INSTALLATION REQUIRED)
# -----------------------------------------------------------------------------

# PyTorch (Deep Learning Backend)
# Install separately based on your system's CUDA version for GPU support.
# See: https://pytorch.org/get-started/locally/
# Example: pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
# # torch>=2.0.0

# ZED SDK (Stereo Camera)
# The ZED SDK from Stereolabs must be installed first.
# Then, install the Python API wrapper.
# See: https://www.stereolabs.com/developers/release/
# # pyzed

# ROS2 (Robot Operating System)
# Required only for robot control (Husky).
# Must be installed and sourced separately. Not installed via pip.
# See: https://docs.ros.org/foxy/Installation.html
# # rclpy
# # geometry_msgs

# -----------------------------------------------------------------------------
# DEVELOPMENT (Optional)
# -----------------------------------------------------------------------------
# jupyter>=1.0.0
# matplotlib>=3.5.0
# pandas>=1.5.0