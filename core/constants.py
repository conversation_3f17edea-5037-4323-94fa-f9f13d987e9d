"""
Application constants for pAIrSEEption.

This module contains all configuration-derived constants and settings
used throughout the application. Constants are loaded from config.yaml
and converted to appropriate types and formats.
"""

import os
from .config_loader import get_config_section, get_config_value


def initialize_constants():
    """
    Initialize all application constants from configuration.
    This function should be called after the configuration is loaded.
    """
    global OPENROUTER_API_KEY, OPENROUTER_API_URL, OLLAMA_API_URL
    global AVAILABLE_YOLO_MODELS, DEFAULT_YOLO_MODEL, YOLO_MODEL_PATH
    global AVAILABLE_ONLINE_MODELS, DEFAULT_ONLINE_MODEL
    global AVAILABLE_OLLAMA_MODELS, DEFAULT_OLLAMA_MODEL
    global ZED_RESOLUTION_NAME, ZED_FPS, ZED_DEPTH_MODE_NAME, ZED_COORDINATE_SYSTEM_NAME
    global ZED_RESOLUTION, ZED_DEPTH_MODE, ZED_COORDINATE_SYSTEM
    global INITIAL_CONFIDENCE_THRESHOLD, ROBOT_CONTROL_AVAILABLE
    global WHISPER_MODEL_NAME, TTS_MODEL_NAME, TTS_USE_GPU
    global IMAGE_SAVE_DIR, ANNOTATED_IMAGE_SAVE_DIR, RESPONSE_LOG_DIR
    global OLLAMA_IMAGE_SAVE_DIR, OLLAMA_ANNOTATED_IMAGE_SAVE_DIR, OLLAMA_RESPONSE_LOG_DIR
    global OBJECT_LOG_CSV, DISTANCE_LOG_CSV, PERFORMANCE_LOG_CSV, TTS_OUTPUT_DIR
    global ZED_DETECTION_CONFIDENCE_THRESHOLD, ZED_ENABLE_TRACKING, ZED_ENABLE_SEGMENTATION
    
    # API and Service Configuration
    api_config = get_config_section('api', {})
    openrouter_config = api_config.get('openrouter', {})
    ollama_config = api_config.get('ollama', {})
    
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY',
                                   get_config_value('api.openrouter.api_key', ''))
    base_url = get_config_value('api.openrouter.base_url', 'https://openrouter.ai/api/v1')
    OPENROUTER_API_URL = f"{base_url}/chat/completions" if base_url else None

    ollama_host = get_config_value('api.ollama.host', 'http://[::1]')
    ollama_port = get_config_value('api.ollama.port', 11434)
    OLLAMA_API_URL = f"{ollama_host}:{ollama_port}/api/chat" if ollama_host else None
    
    # Model Selection
    models_config = get_config_section('models', {})
    yolo_models_config = models_config.get('yolo', {})
    online_llm_config = models_config.get('online_llm', {})
    ollama_llm_config = models_config.get('ollama_llm', {})
    
    AVAILABLE_YOLO_MODELS = get_config_value('models.yolo.available', {})
    DEFAULT_YOLO_MODEL = get_config_value('models.yolo.default', 
                                          list(AVAILABLE_YOLO_MODELS.keys())[0] if AVAILABLE_YOLO_MODELS else '')
    YOLO_MODEL_PATH = AVAILABLE_YOLO_MODELS.get(DEFAULT_YOLO_MODEL, '')
    
    AVAILABLE_ONLINE_MODELS = get_config_value('models.online_llm.available', {})
    DEFAULT_ONLINE_MODEL = get_config_value('models.online_llm.default',
                                            list(AVAILABLE_ONLINE_MODELS.keys())[0] if AVAILABLE_ONLINE_MODELS else '')
    
    AVAILABLE_OLLAMA_MODELS = get_config_value('models.ollama_llm.available', {})
    DEFAULT_OLLAMA_MODEL = get_config_value('models.ollama_llm.default',
                                            list(AVAILABLE_OLLAMA_MODELS.keys())[0] if AVAILABLE_OLLAMA_MODELS else '')
    
    # ZED Camera Settings (will be set when ZED SDK is imported)
    ZED_RESOLUTION_NAME = get_config_value('zed.resolution', 'HD720')
    ZED_FPS = get_config_value('zed.fps', 60)
    ZED_DEPTH_MODE_NAME = get_config_value('zed.depth_mode', 'PERFORMANCE')
    ZED_COORDINATE_SYSTEM_NAME = get_config_value('zed.coordinate_system', 'RIGHT_HANDED_Z_UP_X_FWD')

    # These will be set later when ZED SDK is available
    ZED_RESOLUTION = None
    ZED_DEPTH_MODE = None
    ZED_COORDINATE_SYSTEM = None
    
    # Application Settings
    INITIAL_CONFIDENCE_THRESHOLD = get_config_value('application.initial_confidence', 0.4)
    ROBOT_CONTROL_AVAILABLE = get_config_value('application.robot_control_enabled', True)
    
    # Speech and Audio Settings
    WHISPER_MODEL_NAME = get_config_value('speech.whisper_model_name', 'base')
    TTS_MODEL_NAME = get_config_value('speech.tts_model', 'tts_models/de/thorsten/vits')
    TTS_USE_GPU = get_config_value('speech.tts_use_gpu', False)
    
    # File and Directory Paths
    IMAGE_SAVE_DIR = get_config_value('paths.online_llm_images', 'image_to_online_llm')
    ANNOTATED_IMAGE_SAVE_DIR = get_config_value('paths.online_llm_annotated_images', 'obj_seg_imgs')
    RESPONSE_LOG_DIR = get_config_value('paths.online_llm_responses', 'response_log')
    OLLAMA_IMAGE_SAVE_DIR = get_config_value('paths.ollama_llm_images', 'image_to_ollama_llm')
    OLLAMA_ANNOTATED_IMAGE_SAVE_DIR = get_config_value('paths.ollama_llm_annotated_images', 'obj_seg_ollama_imgs')
    OLLAMA_RESPONSE_LOG_DIR = get_config_value('paths.ollama_llm_responses', 'response_ollama_log')
    OBJECT_LOG_CSV = get_config_value('paths.object_log_csv', 'object_log.csv')
    DISTANCE_LOG_CSV = get_config_value('paths.distance_log_csv', 'relative_distances.csv')
    PERFORMANCE_LOG_CSV = get_config_value('paths.performance_log_csv', 'performance_stats.csv')
    TTS_OUTPUT_DIR = get_config_value('paths.tts_output', 'tts_output')
    
    # ZED SDK Object Detection Settings
    ZED_DETECTION_CONFIDENCE_THRESHOLD = get_config_value('zed_object_detection.detection_confidence_threshold', 15)
    ZED_ENABLE_TRACKING = get_config_value('zed_object_detection.enable_tracking', True)
    ZED_ENABLE_SEGMENTATION = get_config_value('zed_object_detection.enable_segmentation', True)


# Initialize placeholder variables (will be set by initialize_constants())
OPENROUTER_API_KEY = None
OPENROUTER_API_URL = None
OLLAMA_API_URL = None

AVAILABLE_YOLO_MODELS = {}
DEFAULT_YOLO_MODEL = ""
YOLO_MODEL_PATH = ""

AVAILABLE_ONLINE_MODELS = {}
DEFAULT_ONLINE_MODEL = ""

AVAILABLE_OLLAMA_MODELS = {}
DEFAULT_OLLAMA_MODEL = ""

ZED_RESOLUTION_NAME = "HD720"
ZED_FPS = 60
ZED_DEPTH_MODE_NAME = "PERFORMANCE"
ZED_COORDINATE_SYSTEM_NAME = "RIGHT_HANDED_Z_UP_X_FWD"

# These will be set when ZED SDK is imported
ZED_RESOLUTION = None
ZED_DEPTH_MODE = None
ZED_COORDINATE_SYSTEM = None

INITIAL_CONFIDENCE_THRESHOLD = 0.4
ROBOT_CONTROL_AVAILABLE = True

WHISPER_MODEL_NAME = "base"
TTS_MODEL_NAME = "tts_models/de/thorsten/vits"
TTS_USE_GPU = False

IMAGE_SAVE_DIR = "image_to_online_llm"
ANNOTATED_IMAGE_SAVE_DIR = "obj_seg_imgs"
RESPONSE_LOG_DIR = "response_log"
OLLAMA_IMAGE_SAVE_DIR = "image_to_ollama_llm"
OLLAMA_ANNOTATED_IMAGE_SAVE_DIR = "obj_seg_ollama_imgs"
OLLAMA_RESPONSE_LOG_DIR = "response_ollama_log"
OBJECT_LOG_CSV = "object_log.csv"
DISTANCE_LOG_CSV = "relative_distances.csv"
PERFORMANCE_LOG_CSV = "performance_stats.csv"
TTS_OUTPUT_DIR = "tts_output"

ZED_DETECTION_CONFIDENCE_THRESHOLD = 15
ZED_ENABLE_TRACKING = True
ZED_ENABLE_SEGMENTATION = True


def initialize_zed_constants():
    """
    Initialize ZED SDK specific constants.
    This should be called after ZED SDK is imported.
    """
    global ZED_RESOLUTION, ZED_DEPTH_MODE, ZED_COORDINATE_SYSTEM

    try:
        import pyzed.sl as sl

        ZED_RESOLUTION = getattr(sl.RESOLUTION, ZED_RESOLUTION_NAME.upper())
        ZED_DEPTH_MODE = getattr(sl.DEPTH_MODE, ZED_DEPTH_MODE_NAME.upper())
        ZED_COORDINATE_SYSTEM = getattr(sl.COORDINATE_SYSTEM, ZED_COORDINATE_SYSTEM_NAME.upper())

        print(f"✅ ZED constants initialized: {ZED_RESOLUTION_NAME}, {ZED_DEPTH_MODE_NAME}, {ZED_COORDINATE_SYSTEM_NAME}")

    except ImportError as e:
        print(f"⚠️  ZED SDK not available: {e}")
        # Set fallback values
        ZED_RESOLUTION = None
        ZED_DEPTH_MODE = None
        ZED_COORDINATE_SYSTEM = None
