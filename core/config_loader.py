"""
Configuration loader for pAIrSEEption application.

This module handles loading and parsing of the YAML configuration file,
providing a centralized way to access application settings.
"""

import os
import sys
import yaml
from typing import Dict, Any, Optional

# Global configuration storage
_config: Optional[Dict[str, Any]] = None


def load_config(config_path: str = 'config.yaml') -> Dict[str, Any]:
    """
    Loads the YAML configuration file.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dictionary containing the configuration
        
    Raises:
        SystemExit: If configuration file is not found or invalid
    """
    global _config
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            _config = yaml.safe_load(f)
        print(f"Configuration loaded successfully from {config_path}")
        return _config
    except FileNotFoundError:
        print(f"ERROR: Configuration file not found at {config_path}")
        sys.exit(1)
    except yaml.YAMLError as e:
        print(f"ERROR: Could not parse configuration file: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"ERROR: Unexpected error loading configuration: {e}")
        sys.exit(1)


def get_config() -> Dict[str, Any]:
    """
    Get the currently loaded configuration.
    
    Returns:
        Dictionary containing the configuration
        
    Raises:
        RuntimeError: If configuration has not been loaded yet
    """
    global _config
    
    if _config is None:
        raise RuntimeError("Configuration not loaded. Call load_config() first.")
    
    return _config


def get_config_section(section: str, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Get a specific section from the configuration.
    
    Args:
        section: Name of the configuration section
        default: Default value if section doesn't exist
        
    Returns:
        Dictionary containing the section configuration
    """
    config = get_config()
    return config.get(section, default or {})


def get_config_value(key_path: str, default: Any = None) -> Any:
    """
    Get a specific value from the configuration using dot notation.
    
    Args:
        key_path: Dot-separated path to the configuration value (e.g., 'api.openrouter.api_key')
        default: Default value if key doesn't exist
        
    Returns:
        The configuration value
        
    Example:
        >>> get_config_value('zed.resolution', 'HD720')
        'HD720'
    """
    config = get_config()
    keys = key_path.split('.')
    
    current = config
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    
    return current


def reload_config(config_path: str = 'config.yaml') -> Dict[str, Any]:
    """
    Reload the configuration from file.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dictionary containing the reloaded configuration
    """
    return load_config(config_path)


def is_config_loaded() -> bool:
    """
    Check if configuration has been loaded.
    
    Returns:
        True if configuration is loaded, False otherwise
    """
    return _config is not None
