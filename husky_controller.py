#!/usr/bin/env python3

"""
Husky Controller for pAIrSEEption
Simple interface to control the Husky UGV robot based on detected objects
"""

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
import threading
import time
import numpy as np
import math

# Import PySide6 for TTS communication
try:
    from PySide6.QtCore import QMetaObject, Qt, Q_ARG
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

class HuskyController:
    """
    Controller for the Husky UGV robot.
    Controls the robot to a selected target position.
    """
    
    def __init__(self, cmd_vel_topic="/a200_0930/cmd_vel", tts_worker=None):
        """
        Initialize the ROS2 node and publisher

        Args:
            cmd_vel_topic: ROS2 topic for velocity commands
            tts_worker: TTS worker for voice announcements (optional)
        """
        # Initialize ROS2 node
        if not rclpy.ok():
            rclpy.init()

        self.node = rclpy.create_node('husky_controller')
        self.cmd_vel_publisher = self.node.create_publisher(Twist, cmd_vel_topic, 10)
        self.node.get_logger().info(f"<PERSON>sky Controller started, publishing to {cmd_vel_topic}")

        # TTS worker for voice announcements
        self.tts_worker = tts_worker

        # Parameters for navigation
        self.max_linear_speed = 1.0  # m/s
        self.max_angular_speed = 1.5  # rad/s
        self.goal_tolerance = 0.5    # m
        self.target_distance = 1.5   # m, distance to target

        # Camera-based orientation parameters
        self.image_width = 1280  # ZED camera width (will be updated)
        self.image_height = 720  # ZED camera height (will be updated)
        self.center_tolerance = 50  # pixels - tolerance for "centered" object
        self.orientation_gain = 0.8  # gain for angular velocity based on image position

        # Status
        self.is_running = False
        self.current_target = None
        self.current_target_id = None  # Store ID for tracking
        self.current_target_bbox = None  # Store bounding box for image-based orientation
        self.robot_position = [0.0, 0.0, 0.0]  # Robot position (determined by the ZED camera)
        self.follow_mode = False  # Follow mode flag

        # Threading
        self.thread = None
        self.lock = threading.RLock()

        # ROS2 spinner in separate thread
        self.spin_thread = threading.Thread(target=self._ros_spin)
        self.spin_thread.daemon = True
        self.spin_thread.start()
    
    def _ros_spin(self):
        """ROS2 spinner in separate thread"""
        rate = 0.01  # seconds
        while rclpy.ok():
            rclpy.spin_once(self.node, timeout_sec=0.01)
            time.sleep(rate)
    
    def navigate_to_object(self, object_id, object_position, stop_at_distance=None, follow_mode=False, bounding_box=None):
        """
        Start navigation to an object with continuous tracking

        Args:
            object_id: ID of the object to navigate to (for tracking)
            object_position: [x, y, z] position of the target object
            stop_at_distance: distance to the object at which to stop (optional)
            follow_mode: if True, continuously follow the object maintaining distance (optional)
            bounding_box: [x1, y1, x2, y2] bounding box for image-based orientation (optional)
        """
        if stop_at_distance is not None:
            self.target_distance = stop_at_distance

        with self.lock:
            self.current_target = object_position
            self.current_target_id = object_id  # Store ID to enable updates and safety checks
            self.current_target_bbox = bounding_box  # Store bounding box for orientation
            self.follow_mode = follow_mode  # Store follow mode flag
            self.is_running = True
        
        # Start navigation thread
        if self.thread is not None and self.thread.is_alive():
            # Thread already running, will use updated target
            self.node.get_logger().info(f"Updating navigation target to object ID {object_id}")
        else:
            self.thread = threading.Thread(target=self._navigation_loop)
            self.thread.daemon = True
            self.thread.start()
            self.node.get_logger().info(f"Starting navigation to object ID {object_id} at position {object_position}")
        
        return True
    
    def update_target_position(self, object_id, new_position, new_bounding_box=None):
        """
        Update the target position if we're currently navigating to this object

        Args:
            object_id: ID of the object being updated
            new_position: New [x, y, z] position of the object
            new_bounding_box: New [x1, y1, x2, y2] bounding box (optional)
        """
        with self.lock:
            if self.is_running and hasattr(self, 'current_target_id') and self.current_target_id == object_id:
                old_position = self.current_target
                self.current_target = new_position
                self.current_target_bbox = new_bounding_box  # Update bounding box

                # Log significant position changes (optional)
                if old_position:
                    dx = new_position[0] - old_position[0]
                    dy = new_position[1] - old_position[1]
                    change = math.sqrt(dx*dx + dy*dy)
                    if change > 0.5:  # Only log significant changes (> 0.5m)
                        self.node.get_logger().info(f"Target position changed by {change:.2f}m: {new_position}")
    
    def stop(self):
        """Stop navigation and the robot"""
        with self.lock:
            self.is_running = False
            self.current_target = None
            self.current_target_id = None  # Clear target ID
            self.follow_mode = False  # Clear follow mode
        
        # Send stop command
        stop_msg = Twist()
        stop_msg.linear.x = 0.0
        stop_msg.linear.y = 0.0
        stop_msg.linear.z = 0.0
        stop_msg.angular.x = 0.0
        stop_msg.angular.y = 0.0
        stop_msg.angular.z = 0.0
        self.cmd_vel_publisher.publish(stop_msg)
        
        self.node.get_logger().info("Navigation stopped")
    
    def set_robot_position(self, position):
        """
        Update the robot position
        Called by ZED camera tracking
        """
        with self.lock:
            self.robot_position = position

    def set_camera_dimensions(self, width, height):
        """
        Update camera dimensions for image-based orientation

        Args:
            width: Camera image width in pixels
            height: Camera image height in pixels
        """
        with self.lock:
            self.image_width = width
            self.image_height = height
            self.node.get_logger().info(f"Camera dimensions updated: {width}x{height}")
    
    def _navigation_loop(self):
        """Main loop for navigation"""
        rate = 0.1  # seconds
        
        while rclpy.ok():
            with self.lock:
                if not self.is_running or self.current_target is None:
                    break
                
                target_pos = self.current_target
                robot_pos = self.robot_position
            
            # Calculate direction to target
            # Assumption: Position is relative to camera
            # x: forward/backward, y: left/right, z: up/down
            dx = target_pos[0] - robot_pos[0]
            dy = target_pos[1] - robot_pos[1]
            
            # Calculate distance to target
            distance = math.sqrt(dx*dx + dy*dy)
            
            # Check if we're in follow mode or navigation mode
            with self.lock:
                current_follow_mode = self.follow_mode

            if current_follow_mode:
                # Follow mode: maintain distance, don't stop when close
                if distance <= self.target_distance:
                    # We're close enough - stop moving but don't exit the loop
                    cmd = Twist()  # Zero velocity
                    self.cmd_vel_publisher.publish(cmd)
                    time.sleep(rate)
                    continue  # Stay in the loop, keep following
            else:
                # Navigation mode: stop when target is reached
                if distance <= self.target_distance:
                    self.stop()
                    # TTS announcement for goal reached
                    if self.tts_worker and PYSIDE6_AVAILABLE:
                        QMetaObject.invokeMethod(self.tts_worker, "speak_navigation_text", Qt.QueuedConnection, Q_ARG(str, "Ziel erreicht!"))
                    self.node.get_logger().info("Goal reached!")
                    break
            
            # Calculate movement commands
            cmd = Twist()

            # Get current bounding box for image-based orientation
            current_bbox = None
            with self.lock:
                current_bbox = self.current_target_bbox

            # Determine angular velocity based on available information
            angular_speed = 0.0

            if current_bbox is not None and len(current_bbox) == 4:
                # Use image-based orientation (your idea!)
                x1, y1, x2, y2 = current_bbox
                bbox_center_x = (x1 + x2) / 2
                image_center_x = self.image_width / 2

                # Calculate deviation from image center (pixels)
                pixel_deviation = bbox_center_x - image_center_x

                # Convert to normalized deviation (-1 to 1)
                normalized_deviation = pixel_deviation / (self.image_width / 2)

                # Only rotate if object is significantly off-center
                if abs(pixel_deviation) > self.center_tolerance:
                    angular_speed = -self.orientation_gain * normalized_deviation
                    angular_speed = min(max(-self.max_angular_speed, angular_speed), self.max_angular_speed)
                    self.node.get_logger().info(f"Image-based orientation: deviation={pixel_deviation:.1f}px, angular_speed={angular_speed:.2f}")
                else:
                    # Object is centered, no rotation needed
                    angular_speed = 0.0
            else:
                # Fallback to 3D position-based orientation
                angle_to_target = math.atan2(dy, dx)
                angular_speed = min(max(-self.max_angular_speed, angle_to_target * 0.5), self.max_angular_speed)

            # Linear velocity proportional to distance
            linear_speed = min(self.max_linear_speed, 0.3 * distance)
            
            # Set velocities
            cmd.linear.x = linear_speed
            cmd.angular.z = angular_speed
            
            # Send command
            self.cmd_vel_publisher.publish(cmd)
            
            # Short pause
            time.sleep(rate)
    
    def shutdown(self):
        """Shut down the controller and ROS2 node"""
        self.stop()
        self.node.destroy_node()
        # rclpy.shutdown() # Don't call if other ROS2 nodes are running

# Test the controller if executed directly
if __name__ == "__main__":
    controller = HuskyController()
    
    try:
        # Example: Navigate to point [0.3, 0.0, 0.0] (0.3m forward)
        controller.navigate_to_object(1, [0.3, 0.0, 0.0])
        print("Navigation started. Press Ctrl+C to exit.")
        
        # Wait for user interruption
        while True:
            time.sleep(1.0)
            
    except KeyboardInterrupt:
        print("Exiting...")
    finally:
        controller.shutdown() 