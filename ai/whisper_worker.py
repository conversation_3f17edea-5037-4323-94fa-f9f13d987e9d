"""
Whisper Speech Recognition Worker for pAIrSEEption

This module handles speech recognition using Faster-Whisper with GPU-first approach.
"""

import os
import threading
import time
import sounddevice as sd
import numpy as np
from PySide6.QtCore import QObject, Signal, Slot

# Import configuration constants
from core import WHISPER_MODEL_NAME

# For Faster-Whisper STT
try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    print("Faster-Whisper not available. Please install: pip install faster-whisper")
    FASTER_WHISPER_AVAILABLE = False
    WhisperModel = None

def generate_request_id():
    """Generate a unique request ID for tracking"""
    import uuid
    return str(uuid.uuid4())[:8]


class WhisperWorker(QObject):
    """
    Worker class for speech recognition using Faster-Whisper.
    Supports GPU-first approach with CPU fallback.
    """
    transcription_ready = Signal(str)
    status_update = Signal(str)
    finished = Signal()

    def __init__(self, model_size=None):
        super().__init__()
        self._running = False
        self._lock = threading.Lock()
        self.model_size = model_size or WHISPER_MODEL_NAME
        self.model = None
        self.language = None  # Auto-detect by default
        self.recording_duration = 5.0  # Default recording duration
        self.save_audio_files = False
        self.is_recording = False
        self.processing_audio = False

    def set_language(self, language_code):
        """Set the language for transcription"""
        self.language = language_code
        self.status_update.emit(f"WhisperWorker: Language set to {language_code or 'auto-detect'}")

    def set_recording_duration(self, duration):
        """Set the recording duration in seconds"""
        self.recording_duration = duration
        self.status_update.emit(f"WhisperWorker: Recording duration set to {duration}s")

    def set_save_audio_files(self, save_files):
        """Enable/disable saving audio files"""
        self.save_audio_files = save_files
        self.status_update.emit(f"WhisperWorker: Save audio files {'enabled' if save_files else 'disabled'}")

    @Slot()
    def load_whisper_model(self):
        """Load the Whisper model (called via QMetaObject.invokeMethod)"""
        self.initialize_model()

    def initialize_model(self):
        """Initialize the Faster-Whisper model with GPU-first approach"""
        if not FASTER_WHISPER_AVAILABLE:
            self.status_update.emit("WhisperWorker Error: Faster-Whisper not available")
            return False

        try:
            # Map common model names to actual model names
            model_mapping = {
                "tiny": "tiny",
                "base": "base", 
                "small": "small",
                "medium": "medium",
                "large": "large-v3"
            }
            actual_model_name = model_mapping.get(self.model_size, self.model_size)

            # GPU-First approach: Try GPU first, fallback to CPU if needed
            try:
                self.status_update.emit(f"WhisperWorker: Loading Faster-Whisper model '{self.model_size}' ({actual_model_name}) on GPU...")

                # Try GPU with optimized settings for CTranslate2 4.4.0
                self.model = WhisperModel(
                    actual_model_name,
                    device="cuda",
                    compute_type="float16",  # Use float16 for better GPU performance
                    cpu_threads=4,
                    num_workers=1
                )

                # Test GPU model with a small dummy array
                test_audio = np.zeros(1600, dtype=np.float32)  # 0.1 seconds of silence
                segments, info = self.model.transcribe(test_audio, language=self.language)
                list(segments)  # Force evaluation to test GPU functionality

                self.status_update.emit(f"WhisperWorker: GPU model loaded successfully - {actual_model_name}")
                return True

            except Exception as gpu_error:
                self.status_update.emit(f"WhisperWorker: GPU loading failed: {gpu_error}")
                # Continue to CPU fallback below

        except Exception as e:
            self.status_update.emit(f"WhisperWorker: GPU loading failed: {e}")

        # Fallback to CPU if GPU fails
        try:
            self.status_update.emit(f"WhisperWorker: Falling back to CPU for model '{self.model_size}' ({actual_model_name})...")
            self.model = WhisperModel(
                actual_model_name,
                device="cpu",
                compute_type="int8",  # Use int8 for CPU efficiency
                cpu_threads=4,
                num_workers=1
            )
            self.status_update.emit(f"WhisperWorker: CPU model loaded successfully - {actual_model_name}")
            return True

        except Exception as cpu_error:
            self.status_update.emit(f"WhisperWorker: CPU loading also failed: {cpu_error}")
            return False

    def _save_audio_file(self, audio_data, sample_rate=16000, request_id=None):
        """Save audio data to file for debugging"""
        try:
            import soundfile as sf
            
            # Create audio directory if it doesn't exist
            audio_dir = "audio_recordings"
            os.makedirs(audio_dir, exist_ok=True)
            
            # Generate filename with timestamp and request ID
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"whisper_audio_{timestamp}_{request_id or 'unknown'}.wav"
            filepath = os.path.join(audio_dir, filename)
            
            # Save audio file
            sf.write(filepath, audio_data, sample_rate)
            return filepath, request_id
            
        except Exception as e:
            self.status_update.emit(f"WhisperWorker: Error saving audio file: {e}")
            return None, request_id

    @Slot()
    def record_and_transcribe(self):
        """Record audio and transcribe using Faster-Whisper"""
        if not self.model:
            if not self.initialize_model():
                self.status_update.emit("WhisperWorker: Model initialization failed")
                return
        
        try:
            # Generate a unique request ID for this transcription session
            request_id = generate_request_id()
            self.status_update.emit(f"WhisperWorker: Starting recording with request ID: {request_id}")
            
            self.is_recording = True
            
            # Record for optimized duration (configurable for faster response)
            duration = self.recording_duration
            sample_rate = 16000
            audio_data = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='float32')
            sd.wait()
            
            self.is_recording = False
            self.status_update.emit(f"WhisperWorker: Recording finished for request {request_id}. Processing...")

            self.status_update.emit(f"WhisperWorker: Processing audio for request {request_id}...")
            self.processing_audio = True
            audio_np = audio_data.flatten()
            
            # Optional: Save audio file for debugging
            saved_file = None
            if self.save_audio_files:
                saved_file, _ = self._save_audio_file(audio_np, sample_rate=16000, request_id=request_id)
                if saved_file:
                    self.status_update.emit(f"WhisperWorker: Audio saved to {saved_file} with request ID: {request_id}")
            
            # Transcribe using Faster-Whisper
            segments, info = self.model.transcribe(audio_np, language=self.language)
            
            # Extract text from segments
            transcription_text = ""
            for segment in segments:
                transcription_text += segment.text
            
            transcription_text = transcription_text.strip()
            
            if transcription_text:
                self.status_update.emit(f"WhisperWorker: Transcription completed for request {request_id}: '{transcription_text}'")
                self.transcription_ready.emit(transcription_text)
            else:
                self.status_update.emit(f"WhisperWorker: No speech detected for request {request_id}")
                self.transcription_ready.emit("")
                
        except Exception as e:
            self.status_update.emit(f"WhisperWorker: Error during transcription: {e}")
            self.transcription_ready.emit("")
        finally:
            self.processing_audio = False
    
    @Slot()
    def stop_and_transcribe(self):
        """Stop the worker"""
        with self._lock:
            self._running = False
        self.status_update.emit("WhisperWorker: Stopping...")
        self.finished.emit()
