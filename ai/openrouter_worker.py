"""
OpenRouter API Worker for pAIrSEEption

This module handles cloud-based LLM interactions via OpenRouter API.
"""

import os
import threading
import time
import json
import base64
import cv2
import numpy as np
from PySide6.QtCore import QObject, Signal, Slot

# Import configuration constants
from core import (
    OPENROUTER_API_KEY, OPENROUTER_API_URL,
    AVAILABLE_ONLINE_MODELS, DEFAULT_ONLINE_MODEL,
    IMAGE_SAVE_DIR, ANNOTATED_IMAGE_SAVE_DIR, RESPONSE_LOG_DIR
)

# OpenAI client for OpenRouter
try:
    from openai import OpenAI
except ImportError:
    print("OpenAI library not found. Please install: pip install openai")
    OpenAI = None

def generate_request_id():
    """Generate a unique request ID for tracking"""
    import uuid
    return str(uuid.uuid4())[:8]


class OpenRouterWorker(QObject):
    """
    Worker class for cloud-based LLM interactions via OpenRouter API.
    Supports image analysis and chat functionality.
    """
    response_ready = Signal(str)
    status_update = Signal(str)
    finished = Signal()
    chat_response_ready = Signal(str)  # Signal for chat responses
    
    def __init__(self):
        super().__init__()
        self._running = False
        self._lock = threading.Lock()
        self.current_online_model = DEFAULT_ONLINE_MODEL
        self.processing = False
        
        # Initialize OpenAI client for OpenRouter
        if OpenAI is not None and OPENROUTER_API_URL is not None:
            self.client = OpenAI(
                api_key=OPENROUTER_API_KEY,
                base_url=OPENROUTER_API_URL.replace('/chat/completions', '')  # Remove endpoint from base URL
            )
        else:
            self.client = None
            if OpenAI is None:
                self.status_update.emit("OpenRouter Error: OpenAI library not available")
            else:
                self.status_update.emit("OpenRouter Error: API URL not configured")
        
        # Ensure image save directories exist
        self._ensure_directories()
            
    def _ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [IMAGE_SAVE_DIR, ANNOTATED_IMAGE_SAVE_DIR, RESPONSE_LOG_DIR]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def _is_running(self):
        """Thread-safe check if worker is running"""
        with self._lock:
            return self._running
    
    @Slot(str)
    def set_online_model(self, model_name):
        """Set the current online model"""
        if model_name in AVAILABLE_ONLINE_MODELS:
            self.current_online_model = model_name
            self.status_update.emit(f"OpenRouter: Model changed to {model_name}")
        else:
            self.status_update.emit(f"OpenRouter Error: Unknown model {model_name}")
    
    @Slot(str)
    def update_api_key(self, api_key):
        """Update the OpenRouter API key"""
        try:
            if OpenAI is not None and OPENROUTER_API_URL is not None:
                self.client = OpenAI(
                    api_key=api_key,
                    base_url=OPENROUTER_API_URL.replace('/chat/completions', '')
                )
                self.status_update.emit("OpenRouter: API key updated successfully")
            else:
                if OpenAI is None:
                    self.status_update.emit("OpenRouter Error: OpenAI library not available")
                else:
                    self.status_update.emit("OpenRouter Error: API URL not configured")
        except Exception as e:
            self.status_update.emit(f"OpenRouter Error: Failed to update API key: {e}")
    
    def encode_image_to_base64(self, image_np):
        """Encode numpy image to base64 string"""
        try:
            # Convert numpy array to image bytes
            success, buffer = cv2.imencode('.jpg', image_np)
            if not success:
                raise Exception("Failed to encode image")
            
            # Convert to base64
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            return image_base64
        except Exception as e:
            self.status_update.emit(f"OpenRouter Error: Failed to encode image: {e}")
            return None
    
    def save_image_to_disk(self, image_np, object_data, image_type="clean", request_id=None):
        """Save image to disk with request_id for correlation"""
        try:
            # Generate or use provided request_id
            if request_id is None:
                request_id = generate_request_id()
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            if image_type == "clean":
                save_dir = IMAGE_SAVE_DIR
                filename = f"openrouter_clean_{timestamp}_{request_id}.jpg"
            else:
                save_dir = ANNOTATED_IMAGE_SAVE_DIR
                filename = f"openrouter_annotated_{timestamp}_{request_id}.jpg"
            
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, image_np)
            
            return filepath, request_id
        except Exception as e:
            self.status_update.emit(f"OpenRouter Error: Failed to save image: {e}")
            return None, request_id
    
    def format_object_data_for_prompt(self, object_data_list):
        """Format object detection data for LLM prompt"""
        if not object_data_list:
            return "Keine Objekte erkannt."
        
        formatted_objects = []
        for i, obj in enumerate(object_data_list):
            obj_id = obj.get('object_id', 'N/A')
            class_name = obj.get('class_name', 'Unknown')
            position = obj.get('position', [])
            confidence = obj.get('confidence', 0)
            
            # Create clear and simple position description
            if position and len(position) == 3:
                x, y, z = position[0], position[1], position[2]

                # Determine direction (ZED coordinate system: negative Y = right, positive Y = left)
                if abs(y) < 0.3:  # Within 30cm of center
                    direction = "mittig"
                elif y < 0:  # Negative Y = right side
                    direction = "rechts"
                else:  # Positive Y = left side  
                    direction = "links"
                
                distance_str = f"{x:.1f}m"
                position_str = f"{distance_str} {direction}"
            else:
                position_str = "Position unbekannt"
            
            formatted_objects.append(f"- {class_name}: {position_str} (Konfidenz: {confidence:.0%})")
        
        return "\n".join(formatted_objects)
    
    @Slot(object, object, object)
    def process_annotated_scene_request(self, image_np, annotated_image_np, object_data_list):
        """Process scene analysis request with image and object data"""
        if self.processing:
            self.status_update.emit("OpenRouter: Already processing a request, please wait...")
            return
        
        if self.client is None:
            self.status_update.emit("OpenRouter Error: Client not initialized")
            return
        
        try:
            # Save image to disk
            saved_path, request_id = self.save_image_to_disk(image_np, object_data_list, "clean")
            
            # Encode image to base64
            base64_image = self.encode_image_to_base64(image_np)
            if base64_image is None:
                return
            
            self.processing = True
            self.status_update.emit(f"OpenRouter: Processing scene analysis request {request_id}...")
            
            # Format object detection data
            object_info = self.format_object_data_for_prompt(object_data_list)
            
            # Get model ID
            if self.current_online_model not in AVAILABLE_ONLINE_MODELS:
                self.status_update.emit(f"OpenRouter Error: Model {self.current_online_model} not available")
                return
            
            model_id = AVAILABLE_ONLINE_MODELS[self.current_online_model]
            
            # Enhanced system prompt for vision assistant for blind people
            system_prompt = """Du bist ein Assistent für blinde Personen mit ausgezeichneten Bilderkennungsfähigkeiten.
            Analysiere Bilder vollständig und detailliert mit deiner eigenen Vision.
            Erkenne und beschreibe alles was sichtbar ist, auch Details die technische Sensoren übersehen könnten."""

            vision_user_prompt = """Analysiere dieses Bild vollständig mit deiner eigenen Bilderkennung:

BESCHREIBE ALLES WAS DU SIEHST:
- Alle Objekte, Möbel, Gegenstände (auch kleine Details)
- Personen und ihre Aktivitäten
- Räumliche Anordnung und Umgebung
- Farben, Texturen, Beleuchtung
- Besondere Details die für blinde Personen wichtig sind

TECHNISCHE SENSORDATEN (zur Ergänzung):
""" + object_info + """

Gib eine vollständige, detaillierte Beschreibung auf Deutsch. Nutze deine eigene Bilderkennung als Hauptquelle und ergänze mit den Sensordaten."""

            # Make API request
            start_time = time.time()
            
            response = self.client.chat.completions.create(
                model=model_id,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user", 
                        "content": [
                            {"type": "text", "text": vision_user_prompt},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Extract response
            ai_response = response.choices[0].message.content
            
            # Calculate performance statistics
            if hasattr(response, 'usage') and response.usage:
                prompt_tokens = response.usage.prompt_tokens
                completion_tokens = response.usage.completion_tokens
                total_tokens = response.usage.total_tokens
                tokens_per_second = completion_tokens / processing_time if processing_time > 0 else 0
                
                stats_info = f"\n\n[OpenRouter Stats: {total_tokens} tokens ({prompt_tokens} prompt + {completion_tokens} completion), {tokens_per_second:.1f} tokens/s, {processing_time:.1f}s]"
                ai_response += stats_info
            
            # Save response to log file
            self._save_response_log(ai_response, object_data_list, request_id)
            
            self.status_update.emit(f"OpenRouter: Scene analysis completed for request {request_id}")
            self.response_ready.emit(ai_response)
                
        except Exception as e:
            error_msg = str(e)
            self.status_update.emit(f"OpenRouter Error: {error_msg}")
            self.response_ready.emit(f"Error: {error_msg}")
        finally:
            self.processing = False

    def _save_response_log(self, response_text, object_data_list, request_id):
        """Save response to log file"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            log_filename = f"openrouter_response_{timestamp}_{request_id}.txt"
            log_filepath = os.path.join(RESPONSE_LOG_DIR, log_filename)

            with open(log_filepath, 'w', encoding='utf-8') as f:
                f.write(f"OpenRouter Response Log\n")
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Request ID: {request_id}\n")
                f.write(f"Model: {self.current_online_model}\n")
                f.write(f"Object Data: {len(object_data_list)} objects detected\n")
                f.write(f"\n--- Response ---\n")
                f.write(response_text)

        except Exception as e:
            self.status_update.emit(f"OpenRouter Error: Failed to save response log: {e}")

    @Slot(str)
    def process_chat_request(self, user_message):
        """Process a chat request without image"""
        if self.processing:
            self.status_update.emit("OpenRouter: Already processing a request, please wait...")
            return

        if self.client is None:
            self.status_update.emit("OpenRouter Error: Client not initialized")
            return

        try:
            self.processing = True
            request_id = generate_request_id()
            self.status_update.emit(f"OpenRouter: Processing chat request {request_id}...")

            # Get model ID
            if self.current_online_model not in AVAILABLE_ONLINE_MODELS:
                self.status_update.emit(f"OpenRouter Error: Model {self.current_online_model} not available")
                return

            model_id = AVAILABLE_ONLINE_MODELS[self.current_online_model]

            # Make API request
            start_time = time.time()

            response = self.client.chat.completions.create(
                model=model_id,
                messages=[
                    {"role": "user", "content": user_message}
                ],
                max_tokens=1000,
                temperature=0.7
            )

            end_time = time.time()
            processing_time = end_time - start_time

            # Extract response
            ai_response = response.choices[0].message.content

            # Calculate performance statistics
            if hasattr(response, 'usage') and response.usage:
                prompt_tokens = response.usage.prompt_tokens
                completion_tokens = response.usage.completion_tokens
                total_tokens = response.usage.total_tokens
                tokens_per_second = completion_tokens / processing_time if processing_time > 0 else 0

                stats_info = f"\n\n[OpenRouter Stats: {total_tokens} tokens ({prompt_tokens} prompt + {completion_tokens} completion), {tokens_per_second:.1f} tokens/s, {processing_time:.1f}s]"
                ai_response += stats_info

            self.status_update.emit(f"OpenRouter: Chat completed for request {request_id}")
            self.chat_response_ready.emit(ai_response)

        except Exception as e:
            error_msg = str(e)
            self.status_update.emit(f"OpenRouter Error: {error_msg}")
            self.chat_response_ready.emit(f"Error: {error_msg}")
        finally:
            self.processing = False

    @Slot()
    def stop(self):
        """Stop the worker"""
        with self._lock:
            self._running = False
        self.status_update.emit("OpenRouter: Stopping...")
        self.finished.emit()
