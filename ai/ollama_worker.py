"""
Ollama Local LLM Worker for pAIrSEEption

This module handles local LLM interactions via Ollama API.
"""

import os
import threading
import time
import json
import base64
import cv2
import numpy as np
import requests
from PySide6.QtCore import QObject, Signal, Slot

# Import configuration constants
from core import (
    OLLAMA_API_URL,
    AVAILABLE_OLLAMA_MODELS, DEFAULT_OLLAMA_MODEL,
    OLLAMA_IMAGE_SAVE_DIR, OLLAMA_ANNOTATED_IMAGE_SAVE_DIR, OLLAMA_RESPONSE_LOG_DIR
)

def generate_request_id():
    """Generate a unique request ID for tracking"""
    import uuid
    return str(uuid.uuid4())[:8]


class OllamaWorker(QObject):
    """
    Worker class for local LLM interactions via Ollama API.
    Supports image analysis and chat functionality.
    """
    response_ready = Signal(str)
    status_update = Signal(str)
    finished = Signal()
    chat_response_ready = Signal(str)  # Signal for chat responses
    
    def __init__(self):
        super().__init__()
        self._running = False
        self._lock = threading.Lock()
        self.current_ollama_model = DEFAULT_OLLAMA_MODEL
        self.processing = False
        
        # Ensure image save directories exist
        self._ensure_directories()
            
    def _ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [OLLAMA_IMAGE_SAVE_DIR, OLLAMA_ANNOTATED_IMAGE_SAVE_DIR, OLLAMA_RESPONSE_LOG_DIR]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def _is_running(self):
        """Thread-safe check if worker is running"""
        with self._lock:
            return self._running
    
    @Slot(str)
    def set_ollama_model(self, model_name):
        """Set the current Ollama model"""
        if model_name in AVAILABLE_OLLAMA_MODELS:
            self.current_ollama_model = model_name
            self.status_update.emit(f"Ollama: Model changed to {model_name}")
        else:
            self.status_update.emit(f"Ollama Error: Unknown model {model_name}")
    
    def encode_image_to_base64(self, image_np):
        """Encode numpy image to base64 string"""
        try:
            # Convert numpy array to image bytes
            success, buffer = cv2.imencode('.jpg', image_np)
            if not success:
                raise Exception("Failed to encode image")
            
            # Convert to base64
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            return image_base64
        except Exception as e:
            self.status_update.emit(f"Ollama Error: Failed to encode image: {e}")
            return None
    
    def save_image_to_disk(self, image_np, object_data, image_type="clean", request_id=None):
        """Save image to disk with request_id for correlation"""
        try:
            # Generate or use provided request_id
            if request_id is None:
                request_id = generate_request_id()
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            if image_type == "clean":
                save_dir = OLLAMA_IMAGE_SAVE_DIR
                filename = f"ollama_clean_{timestamp}_{request_id}.jpg"
            else:
                save_dir = OLLAMA_ANNOTATED_IMAGE_SAVE_DIR
                filename = f"ollama_annotated_{timestamp}_{request_id}.jpg"
            
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, image_np)
            
            return filepath, request_id
        except Exception as e:
            self.status_update.emit(f"Ollama Error: Failed to save image: {e}")
            return None, request_id
    
    def format_object_data_for_prompt(self, object_data_list):
        """Format object detection data for LLM prompt"""
        if not object_data_list:
            return "Keine Objekte erkannt."
        
        formatted_objects = []
        for i, obj in enumerate(object_data_list):
            obj_id = obj.get('object_id', 'N/A')
            class_name = obj.get('class_name', 'Unknown')
            position = obj.get('position', [])
            confidence = obj.get('confidence', 0)
            
            # Create clear and simple position description
            if position and len(position) == 3:
                x, y, z = position[0], position[1], position[2]

                # Determine direction (ZED coordinate system: negative Y = right, positive Y = left)
                if abs(y) < 0.3:  # Within 30cm of center
                    direction = "mittig"
                elif y < 0:  # Negative Y = right side
                    direction = "rechts"
                else:  # Positive Y = left side  
                    direction = "links"
                
                distance_str = f"{x:.1f}m"
                position_str = f"{distance_str} {direction}"
            else:
                position_str = "Position unbekannt"
            
            formatted_objects.append(f"- {class_name}: {position_str} (Konfidenz: {confidence:.0%})")
        
        return "\n".join(formatted_objects)
    
    @Slot(object, object, object)
    def process_annotated_scene_request(self, image_np, annotated_image_np, object_data_list):
        """Process scene analysis request with image and object data"""
        if self.processing:
            self.status_update.emit("Ollama: Already processing a request, please wait...")
            return
        
        if not OLLAMA_API_URL:
            self.status_update.emit("Ollama Error: API URL not configured")
            return
        
        try:
            # Save image to disk
            saved_path, request_id = self.save_image_to_disk(image_np, object_data_list, "clean")
            
            # Encode image to base64
            base64_image = self.encode_image_to_base64(image_np)
            if base64_image is None:
                return
            
            self.processing = True
            self.status_update.emit(f"Ollama: Processing scene analysis request {request_id}...")
            
            # Format object detection data
            object_info = self.format_object_data_for_prompt(object_data_list)
            
            # Get model ID
            if self.current_ollama_model not in AVAILABLE_OLLAMA_MODELS:
                self.status_update.emit(f"Ollama Error: Model {self.current_ollama_model} not available")
                return
            
            model_id = AVAILABLE_OLLAMA_MODELS[self.current_ollama_model]
            
            # Enhanced system prompt for vision assistant for blind people
            system_prompt = """Du bist ein Assistent für blinde Personen mit ausgezeichneten Bilderkennungsfähigkeiten.
            Analysiere Bilder vollständig und detailliert mit deiner eigenen Vision.
            Erkenne und beschreibe alles was sichtbar ist, auch Details die technische Sensoren übersehen könnten.
            Kombiniere deine Bilderkennung mit technischen Sensordaten zu hilfreichen Beschreibungen."""

            user_prompt = f"""Analysiere dieses Bild vollständig mit deiner eigenen Bilderkennung:

BESCHREIBE ALLES WAS DU SIEHST:
- Alle Objekte, Möbel, Gegenstände (auch kleine Details)
- Personen und ihre Aktivitäten
- Räumliche Anordnung und Umgebung
- Farben, Texturen, Beleuchtung
- Besondere Details die für blinde Personen wichtig sind

TECHNISCHE SENSORDATEN (zur Ergänzung):
{object_info}

Gib eine vollständige, detaillierte Beschreibung auf Deutsch. Nutze deine eigene Bilderkennung als Hauptquelle und ergänze mit den Sensordaten."""

            # Prepare Ollama API request
            payload = {
                "model": model_id,
                "prompt": user_prompt,
                "images": [base64_image],
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 1000
                }
            }
            
            # Make API request
            start_time = time.time()
            
            response = requests.post(
                OLLAMA_API_URL.replace('/chat', '/generate'),  # Use generate endpoint for images
                json=payload,
                timeout=120  # 2 minute timeout for local processing
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', 'No response received')
                
                # Calculate performance statistics
                eval_count = result.get('eval_count', 0)
                eval_duration = result.get('eval_duration', 0)
                
                if eval_count > 0 and eval_duration > 0:
                    # Convert nanoseconds to seconds
                    eval_duration_seconds = eval_duration / 1_000_000_000
                    tokens_per_second = eval_count / eval_duration_seconds if eval_duration_seconds > 0 else 0
                    
                    stats_info = f"\n\n[Ollama Stats: {eval_count} tokens, {tokens_per_second:.1f} tokens/s, {processing_time:.1f}s total]"
                    ai_response += stats_info
                
                # Save response to log file
                self._save_response_log(ai_response, object_data_list, request_id)
                
                self.status_update.emit(f"Ollama: Scene analysis completed for request {request_id}")
                self.response_ready.emit(ai_response)
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.status_update.emit(f"Ollama Error: {error_msg}")
                self.response_ready.emit(f"Error: {error_msg}")
                
        except Exception as e:
            error_msg = str(e)
            self.status_update.emit(f"Ollama Error: {error_msg}")
            self.response_ready.emit(f"Error: {error_msg}")
        finally:
            self.processing = False

    def _save_response_log(self, response_text, object_data_list, request_id):
        """Save response to log file"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            log_filename = f"ollama_response_{timestamp}_{request_id}.txt"
            log_filepath = os.path.join(OLLAMA_RESPONSE_LOG_DIR, log_filename)

            with open(log_filepath, 'w', encoding='utf-8') as f:
                f.write(f"Ollama Response Log\n")
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Request ID: {request_id}\n")
                f.write(f"Model: {self.current_ollama_model}\n")
                f.write(f"Object Data: {len(object_data_list)} objects detected\n")
                f.write(f"\n--- Response ---\n")
                f.write(response_text)

        except Exception as e:
            self.status_update.emit(f"Ollama Error: Failed to save response log: {e}")

    @Slot(str)
    def process_chat_request(self, user_message):
        """Process a chat request without image"""
        if self.processing:
            self.status_update.emit("Ollama: Already processing a request, please wait...")
            return

        if not OLLAMA_API_URL:
            self.status_update.emit("Ollama Error: API URL not configured")
            return

        try:
            self.processing = True
            request_id = generate_request_id()
            self.status_update.emit(f"Ollama: Processing chat request {request_id}...")

            # Get model ID
            if self.current_ollama_model not in AVAILABLE_OLLAMA_MODELS:
                self.status_update.emit(f"Ollama Error: Model {self.current_ollama_model} not available")
                return

            model_id = AVAILABLE_OLLAMA_MODELS[self.current_ollama_model]

            # Prepare Ollama API request
            payload = {
                "model": model_id,
                "messages": [
                    {"role": "user", "content": user_message}
                ],
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 1000
                }
            }

            # Make API request
            start_time = time.time()

            response = requests.post(
                OLLAMA_API_URL,  # Use chat endpoint for text-only
                json=payload,
                timeout=120  # 2 minute timeout for local processing
            )

            end_time = time.time()
            processing_time = end_time - start_time

            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('message', {}).get('content', 'No response received')

                # Calculate performance statistics
                eval_count = result.get('eval_count', 0)
                eval_duration = result.get('eval_duration', 0)

                if eval_count > 0 and eval_duration > 0:
                    # Convert nanoseconds to seconds
                    eval_duration_seconds = eval_duration / 1_000_000_000
                    tokens_per_second = eval_count / eval_duration_seconds if eval_duration_seconds > 0 else 0

                    stats_info = f"\n\n[Ollama Stats: {eval_count} tokens, {tokens_per_second:.1f} tokens/s, {processing_time:.1f}s total]"
                    ai_response += stats_info

                self.status_update.emit(f"Ollama: Chat completed for request {request_id}")
                self.chat_response_ready.emit(ai_response)
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.status_update.emit(f"Ollama Error: {error_msg}")
                self.chat_response_ready.emit(f"Error: {error_msg}")

        except Exception as e:
            error_msg = str(e)
            self.status_update.emit(f"Ollama Error: {error_msg}")
            self.chat_response_ready.emit(f"Error: {error_msg}")
        finally:
            self.processing = False

    @Slot()
    def stop(self):
        """Stop the worker"""
        with self._lock:
            self._running = False
        self.status_update.emit("Ollama: Stopping...")
        self.finished.emit()
